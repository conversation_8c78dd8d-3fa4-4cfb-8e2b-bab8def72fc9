WEBPINFO(1)                 General Commands Manual                WEBPINFO(1)



NAME
       webpinfo - print out the chunk level structure of WebP files along with
       basic integrity checks.

SYNOPSIS
       webpinfo OPTIONS INPUT
       webpinfo [-h|-help|-H|-longhelp]


DESCRIPTION
       This manual page documents the webpinfo command.

       webpinfo can be used to print out the chunk level  structure  and  bit-
       stream header information of WebP files. It can also check if the files
       are of valid WebP format.


OPTIONS
       -version
              Print the version number (as major.minor.revision) and exit.

       -quiet Do not show chunk parsing information.

       -diag  Show parsing error diagnosis.

       -summary
              Show chunk stats summary.

       -bitstream_info
              Parse bitstream header.

       -h, -help
              A short usage summary.

       -H, -longhelp
              Detailed usage instructions.


INPUT
       Input files in WebP format. Input files must come last,  following  op-
       tions (if any). There can be multiple input files.


BUGS
       Please     report     all     bugs     to     the     issue    tracker:
       https://bugs.chromium.org/p/webp
       Patches welcome! See this  page  to  get  started:  http://www.webmpro-
       ject.org/code/contribute/submitting-patches/


EXAMPLES
       webpinfo -h
       webpinfo -diag -summary input_file.webp
       webpinfo -bitstream_info input_file_1.webp input_file_2.webp
       webpinfo *.webp


AUTHORS
       webpinfo is a part of libwebp and was written by the WebP team.
       The   latest  source  tree  is  available  at  https://chromium.google-
       source.com/webm/libwebp

       This manual page was written by Hui Su <<EMAIL>>, for the  De-
       bian project (and may be used by others).


SEE ALSO
       webpmux(1)
       Please refer to http://developers.google.com/speed/webp/ for additional
       information.



                               November 24, 2017                   WEBPINFO(1)
