<!-- Creator     : groff version 1.19.2 -->
<!-- CreationDate: Thu Dec 26 09:17:31 2019 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p     { margin-top: 0; margin-bottom: 0; }
       pre   { margin-top: 0; margin-bottom: 0; }
       table { margin-top: 0; margin-bottom: 0; }
</style>
<title>CWEBP</title>

</head>
<body>

<h1 align=center>CWEBP</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#AUTHORS">AUTHORS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<a name="NAME"></a>
<h2>NAME</h2>


<p style="margin-left:11%; margin-top: 1em">cwebp &minus;
compress an image file to a WebP file</p>

<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>


<p style="margin-left:11%; margin-top: 1em"><b>cwebp</b>
[<i>options</i>] <i>input_file &minus;o
output_file.webp</i></p>

<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>


<p style="margin-left:11%; margin-top: 1em">This manual
page documents the <b>cwebp</b> command.</p>

<p style="margin-left:11%; margin-top: 1em"><b>cwebp</b>
compresses an image using the WebP format. Input format can
be either PNG, JPEG, TIFF, WebP or raw Y&rsquo;CbCr
samples.</p>

<a name="OPTIONS"></a>
<h2>OPTIONS</h2>


<p style="margin-left:11%; margin-top: 1em">The basic
options are: <b><br>
&minus;o</b> <i>string</i></p>

<p style="margin-left:22%;">Specify the name of the output
WebP file. If omitted, <b>cwebp</b> will perform compression
but only report statistics. Using &quot;&minus;&quot; as
output name will direct output to &rsquo;stdout&rsquo;.</p>

<p style="margin-left:11%;"><b>&minus;&minus;</b>
<i>string</i></p>

<p style="margin-left:22%;">Explicitly specify the input
file. This option is useful if the input file starts with a
&rsquo;&minus;&rsquo; for instance. This option must appear
<b>last</b>. Any other options afterward will be
ignored.</p>

<p style="margin-left:11%;"><b>&minus;h,
&minus;help</b></p>

<p style="margin-left:22%;">A short usage summary.</p>

<p style="margin-left:11%;"><b>&minus;H,
&minus;longhelp</b></p>

<p style="margin-left:22%;">A summary of all the possible
options.</p>

<p style="margin-left:11%;"><b>&minus;version</b></p>

<p style="margin-left:22%;">Print the version number (as
major.minor.revision) and exit.</p>

<p style="margin-left:11%;"><b>&minus;lossless</b></p>

<p style="margin-left:22%;">Encode the image without any
loss. For images with fully transparent area, the invisible
pixel values (R/G/B or Y/U/V) will be preserved only if the
&minus;exact option is used.</p>

<p style="margin-left:11%;"><b>&minus;near_lossless</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify the level of
near&minus;lossless image preprocessing. This option adjusts
pixel values to help compressibility, but has minimal impact
on the visual quality. It triggers lossless compression mode
automatically. The range is 0 (maximum preprocessing) to 100
(no preprocessing, the default). The typical value is around
60. Note that lossy with <b>&minus;q 100</b> can at times
yield better results.</p>

<p style="margin-left:11%;"><b>&minus;q</b>
<i>float</i></p>

<p style="margin-left:22%;">Specify the compression factor
for RGB channels between 0 and 100. The default is 75. <br>
In case of lossy compression (default), a small factor
produces a smaller file with lower quality. Best quality is
achieved by using a value of 100. <br>
In case of lossless compression (specified by the
<b>&minus;lossless</b> option), a small factor enables
faster compression speed, but produces a larger file.
Maximum compression is achieved by using a value of 100.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p style="margin-top: 1em" valign="top"><b>&minus;z</b>
<i>int</i></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Switch on
<b>lossless</b> compression mode with the specified level
between 0 and 9, with level 0 being the fastest, 9 being the
slowest. Fast mode produces larger file size than slower
ones. A good default is <b>&minus;z 6</b>. This option is
actually a shortcut for some predefined settings for quality
and method. If options <b>&minus;q</b> or <b>&minus;m</b>
are subsequently used, they will invalidate the effect of
this option.</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;alpha_q</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify the compression factor
for alpha compression between 0 and 100. Lossless
compression of alpha is achieved using a value of 100, while
the lower values result in a lossy compression. The default
is 100.</p>

<p style="margin-left:11%;"><b>&minus;preset</b>
<i>string</i></p>

<p style="margin-left:22%;">Specify a set of
pre&minus;defined parameters to suit a particular type of
source material. Possible values are: <b>default</b>,
<b>photo</b>, <b>picture</b>, <b>drawing</b>, <b>icon</b>,
<b>text</b>. Since <b>&minus;preset</b> overwrites the other
parameters&rsquo; values (except the <b>&minus;q</b> one),
this option should preferably appear first in the order of
the arguments.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p style="margin-top: 1em" valign="top"><b>&minus;m</b>
<i>int</i></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Specify the
compression method to use. This parameter controls the trade
off between encoding speed and the compressed file size and
quality. Possible values range from 0 to 6. Default value is
4. When higher values are used, the encoder will spend more
time inspecting additional encoding possibilities and decide
on the quality gain. Lower value can result in faster
processing time at the expense of larger file size and lower
compression quality.</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;resize</b> <i>width
height</i></p>

<p style="margin-left:22%;">Resize the source to a
rectangle with size <b>width</b> x <b>height</b>. If either
(but not both) of the <b>width</b> or <b>height</b>
parameters is 0, the value will be calculated preserving the
aspect&minus;ratio.</p>

<p style="margin-left:11%;"><b>&minus;crop</b>
<i>x_position y_position width height</i></p>

<p style="margin-left:22%;">Crop the source to a rectangle
with top&minus;left corner at coordinates
(<b>x_position</b>, <b>y_position</b>) and size <b>width</b>
x <b>height</b>. This cropping area must be fully contained
within the source rectangle.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">



<p style="margin-top: 1em" valign="top"><b>&minus;mt</b></p> </td>
<td width="7%"></td>
<td width="70%">


<p style="margin-top: 1em" valign="top">Use
multi&minus;threading for encoding, if possible.</p></td>
<td width="8%">
</td>
</table>

<p style="margin-left:11%;"><b>&minus;low_memory</b></p>

<p style="margin-left:22%;">Reduce memory usage of lossy
encoding by saving four times the compressed size
(typically). This will make the encoding slower and the
output slightly different in size and distortion. This flag
is only effective for methods 3 and up, and is off by
default. Note that leaving this flag off will have some side
effects on the bitstream: it forces certain bitstream
features like number of partitions (forced to 1). Note that
a more detailed report of bitstream size is printed by
<b>cwebp</b> when using this option.</p>

<p style="margin-left:11%; margin-top: 1em"><b>LOSSY
OPTIONS</b> <br>
These options are only effective when doing lossy encoding
(the default, with or without alpha). <b><br>
&minus;size</b> <i>int</i></p>

<p style="margin-left:22%;">Specify a target size (in
bytes) to try and reach for the compressed output. The
compressor will make several passes of partial encoding in
order to get as close as possible to this target. If both
<b>&minus;size</b> and <b>&minus;psnr</b> are used,
<b>&minus;size</b> value will prevail.</p>

<p style="margin-left:11%;"><b>&minus;psnr</b>
<i>float</i></p>

<p style="margin-left:22%;">Specify a target PSNR (in dB)
to try and reach for the compressed output. The compressor
will make several passes of partial encoding in order to get
as close as possible to this target. If both
<b>&minus;size</b> and <b>&minus;psnr</b> are used,
<b>&minus;size</b> value will prevail.</p>

<p style="margin-left:11%;"><b>&minus;pass</b>
<i>int</i></p>

<p style="margin-left:22%;">Set a maximum number of passes
to use during the dichotomy used by options
<b>&minus;size</b> or <b>&minus;psnr</b>. Maximum value is
10, default is 1. If options <b>&minus;size</b> or
<b>&minus;psnr</b> were used, but <b>&minus;pass</b>
wasn&rsquo;t specified, a default value of &rsquo;6&rsquo;
passes will be used.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">



<p style="margin-top: 1em" valign="top"><b>&minus;af</b></p> </td>
<td width="7%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Turns
auto&minus;filter on. This algorithm will spend additional
time optimizing the filtering strength to reach a
well&minus;balanced quality.</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;jpeg_like</b></p>

<p style="margin-left:22%;">Change the internal parameter
mapping to better match the expected size of JPEG
compression. This flag will generally produce an output file
of similar size to its JPEG equivalent (for the same
<b>&minus;q</b> setting), but with less visual
distortion.</p>

<p style="margin-left:11%;">Advanced options:</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p style="margin-top: 1em" valign="top"><b>&minus;f</b>
<i>int</i></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Specify the
strength of the deblocking filter, between 0 (no filtering)
and 100 (maximum filtering). A value of 0 will turn off any
filtering. Higher value will increase the strength of the
filtering process applied after decoding the picture. The
higher the value the smoother the picture will appear.
Typical values are usually in the range of 20 to 50.</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;sharpness</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify the sharpness of the
filtering (if used). Range is 0 (sharpest) to 7 (least
sharp). Default is 0.</p>

<p style="margin-left:11%;"><b>&minus;strong</b></p>

<p style="margin-left:22%;">Use strong filtering (if
filtering is being used thanks to the <b>&minus;f</b>
option). Strong filtering is on by default.</p>

<p style="margin-left:11%;"><b>&minus;nostrong</b></p>

<p style="margin-left:22%;">Disable strong filtering (if
filtering is being used thanks to the <b>&minus;f</b>
option) and use simple filtering instead.</p>

<p style="margin-left:11%;"><b>&minus;sharp_yuv</b></p>

<p style="margin-left:22%;">Use more accurate and sharper
RGB-&gt;YUV conversion if needed. Note that this process is
slower than the default &rsquo;fast&rsquo; RGB-&gt;YUV
conversion.</p>

<p style="margin-left:11%;"><b>&minus;sns</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify the amplitude of the
spatial noise shaping. Spatial noise shaping (or <b>sns</b>
for short) refers to a general collection of built&minus;in
algorithms used to decide which area of the picture should
use relatively less bits, and where else to better transfer
these bits. The possible range goes from 0 (algorithm is
off) to 100 (the maximal effect). The default value is
50.</p>

<p style="margin-left:11%;"><b>&minus;segments</b>
<i>int</i></p>

<p style="margin-left:22%;">Change the number of partitions
to use during the segmentation of the sns algorithm.
Segments should be in range 1 to 4. Default value is 4. This
option has no effect for methods 3 and up, unless
<b>&minus;low_memory</b> is used.</p>

<p style="margin-left:11%;"><b>&minus;partition_limit</b>
<i>int</i></p>

<p style="margin-left:22%;">Degrade quality by limiting the
number of bits used by some macroblocks. Range is 0 (no
degradation, the default) to 100 (full degradation). Useful
values are usually around 30&minus;70 for moderately large
images. In the VP8 format, the so&minus;called control
partition has a limit of 512k and is used to store the
following information: whether the macroblock is skipped,
which segment it belongs to, whether it is coded as intra
4x4 or intra 16x16 mode, and finally the prediction modes to
use for each of the sub&minus;blocks. For a very large
image, 512k only leaves room to few bits per 16x16
macroblock. The absolute minimum is 4 bits per macroblock.
Skip, segment, and mode information can use up almost all
these 4 bits (although the case is unlikely), which is
problematic for very large images. The partition_limit
factor controls how frequently the most bit&minus;costly
mode (intra 4x4) will be used. This is useful in case the
512k limit is reached and the following message is
displayed: <i>Error code: 6 (PARTITION0_OVERFLOW: Partition
#0 is too big to fit 512k)</i>. If using
<b>&minus;partition_limit</b> is not enough to meet the 512k
constraint, one should use less segments in order to save
more header bits per macroblock. See the
<b>&minus;segments</b> option.</p>

<p style="margin-left:11%; margin-top: 1em"><b>LOGGING
OPTIONS</b> <br>
These options control the level of output:</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">



<p style="margin-top: 1em" valign="top"><b>&minus;v</b></p> </td>
<td width="8%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Print extra
information (encoding time in particular).</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;print_psnr</b></p>

<p style="margin-left:22%;">Compute and report average PSNR
(Peak&minus;Signal&minus;To&minus;Noise ratio).</p>

<p style="margin-left:11%;"><b>&minus;print_ssim</b></p>

<p style="margin-left:22%;">Compute and report average SSIM
(structural similarity metric, see
http://en.wikipedia.org/wiki/SSIM for additional
details).</p>

<p style="margin-left:11%;"><b>&minus;print_lsim</b></p>

<p style="margin-left:22%;">Compute and report local
similarity metric (sum of lowest error amongst the
collocated pixel neighbors).</p>

<p style="margin-left:11%;"><b>&minus;progress</b></p>

<p style="margin-left:22%;">Report encoding progress in
percent.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;quiet</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Do not print
anything.</p> </td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;short</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Only print brief
information (output file size and PSNR) for testing
purposes.</p> </td>
</table>

<p style="margin-left:11%;"><b>&minus;map</b>
<i>int</i></p>

<p style="margin-left:22%;">Output additional
ASCII&minus;map of encoding information. Possible map values
range from 1 to 6. This is only meant to help debugging.</p>

<p style="margin-left:11%; margin-top: 1em"><b>ADDITIONAL
OPTIONS</b> <br>
More advanced options are: <b><br>
&minus;s</b> <i>width height</i></p>

<p style="margin-left:22%;">Specify that the input file
actually consists of raw Y&rsquo;CbCr samples following the
ITU&minus;R BT.601 recommendation, in 4:2:0 linear format.
The luma plane has size <b>width</b> x <b>height</b>.</p>

<p style="margin-left:11%;"><b>&minus;pre</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify some preprocessing
steps. Using a value of &rsquo;2&rsquo; will trigger
quality&minus;dependent pseudo&minus;random dithering during
RGBA&minus;&gt;YUVA conversion (lossy compression only).</p>

<p style="margin-left:11%;"><b>&minus;alpha_filter</b>
<i>string</i></p>

<p style="margin-left:22%;">Specify the predictive
filtering method for the alpha plane. One of
&rsquo;none&rsquo;, &rsquo;fast&rsquo; or
&rsquo;best&rsquo;, in increasing complexity and slowness
order. Default is &rsquo;fast&rsquo;. Internally, alpha
filtering is performed using four possible predictions
(none, horizontal, vertical, gradient). The
&rsquo;best&rsquo; mode will try each mode in turn and pick
the one which gives the smaller size. The &rsquo;fast&rsquo;
mode will just try to form an a priori guess without testing
all modes.</p>

<p style="margin-left:11%;"><b>&minus;alpha_method</b>
<i>int</i></p>

<p style="margin-left:22%;">Specify the algorithm used for
alpha compression: 0 or 1. Algorithm 0 denotes no
compression, 1 uses WebP lossless format for compression.
The default is 1.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;exact</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Preserve RGB values
in transparent area. The default is off, to help
compressibility.</p> </td>
</table>

<p style="margin-left:11%;"><b>&minus;blend_alpha</b>
<i>int</i></p>

<p style="margin-left:22%;">This option blends the alpha
channel (if present) with the source using the background
color specified in hexadecimal as 0xrrggbb. The alpha
channel is afterward reset to the opaque value 255.</p>

<p style="margin-left:11%;"><b>&minus;noalpha</b></p>

<p style="margin-left:22%;">Using this option will discard
the alpha channel.</p>

<p style="margin-left:11%;"><b>&minus;hint</b>
<i>string</i></p>

<p style="margin-left:22%;">Specify the hint about input
image type. Possible values are: <b>photo</b>,
<b>picture</b> or <b>graph</b>.</p>

<p style="margin-left:11%;"><b>&minus;metadata</b>
<i>string</i></p>

<p style="margin-left:22%;">A comma separated list of
metadata to copy from the input to the output if present.
Valid values: <b>all</b>, <b>none</b>, <b>exif</b>,
<b>icc</b>, <b>xmp</b>. The default is <b>none</b>.</p>

<p style="margin-left:22%; margin-top: 1em">Note: each
input format may not support all combinations.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;noasm</b></p> </td>
<td width="2%"></td>
<td width="53%">


<p style="margin-top: 1em" valign="top">Disable all
assembly optimizations.</p></td>
<td width="25%">
</td>
</table>

<a name="BUGS"></a>
<h2>BUGS</h2>


<p style="margin-left:11%; margin-top: 1em">Please report
all bugs to the issue tracker:
https://bugs.chromium.org/p/webp <br>
Patches welcome! See this page to get started:
http://www.webmproject.org/code/contribute/submitting&minus;patches/</p>

<a name="EXAMPLES"></a>
<h2>EXAMPLES</h2>


<p style="margin-left:11%; margin-top: 1em">cwebp &minus;q
50 -lossless picture.png &minus;o picture_lossless.webp <br>
cwebp &minus;q 70 picture_with_alpha.png &minus;o
picture_with_alpha.webp <br>
cwebp &minus;sns 70 &minus;f 50 &minus;size 60000
picture.png &minus;o picture.webp <br>
cwebp &minus;o picture.webp &minus;&minus;
&minus;&minus;&minus;picture.png</p>

<a name="AUTHORS"></a>
<h2>AUTHORS</h2>


<p style="margin-left:11%; margin-top: 1em"><b>cwebp</b> is
a part of libwebp and was written by the WebP team. <br>
The latest source tree is available at
https://chromium.googlesource.com/webm/libwebp</p>

<p style="margin-left:11%; margin-top: 1em">This manual
page was written by Pascal Massimino
&lt;<EMAIL>&gt;, for the Debian project
(and may be used by others).</p>

<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>



<p style="margin-left:11%; margin-top: 1em"><b>dwebp</b>(1),
<b>gif2webp</b>(1) <br>
Please refer to http://developers.google.com/speed/webp/ for
additional information.</p>
<hr>
</body>
</html>
