import dotenv from 'dotenv';
import envValidator from './envValidator.js';

// Load environment variables
dotenv.config();

/**
 * Secure Configuration Manager
 * Validates and provides secure access to environment variables
 */

class ConfigManager {
  constructor() {
    this.isInitialized = false;
    this.config = {};
    this.validationResult = null;
  }

  /**
   * Initialize and validate configuration
   */
  initialize() {
    if (this.isInitialized) {
      return this.validationResult;
    }

    console.log('🚀 Initializing secure configuration...');
    
    this.validationResult = envValidator.validate();
    
    if (!this.validationResult.isValid) {
      console.error('\n💥 Configuration validation failed. Server cannot start safely.');
      console.error('Please fix the environment configuration errors above.');
      
      // In development, offer to generate secure secrets
      if (process.env.NODE_ENV === 'development') {
        console.log('\n💡 Need secure secrets? Run this command to generate them:');
        console.log('node -e "import(\'./src/config/envValidator.js\').then(m => m.default.generateDevelopmentSecrets())"');
      }
      
      process.exit(1);
    }

    this.config = this.validationResult.config;
    this.isInitialized = true;

    // Log configuration summary (without sensitive data)
    this.logConfigSummary();

    return this.validationResult;
  }

  /**
   * Get configuration value safely
   */
  get(key, defaultValue = null) {
    if (!this.isInitialized) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }

    return this.config[key] || defaultValue;
  }

  /**
   * Check if running in development mode
   */
  isDevelopment() {
    return this.get('NODE_ENV') === 'development';
  }

  /**
   * Check if running in production mode
   */
  isProduction() {
    return this.get('NODE_ENV') === 'production';
  }

  /**
   * Check if running in test mode
   */
  isTest() {
    return this.get('NODE_ENV') === 'test';
  }

  /**
   * Check if fake auth mode is enabled
   */
  isFakeAuthMode() {
    return this.get('FAKE_AUTH_MODE') === 'true';
  }

  /**
   * Get database configuration
   */
  getDatabaseConfig() {
    return {
      url: this.get('DATABASE_URL'),
      isSQLite: this.get('DATABASE_URL').startsWith('file:'),
      isPostgreSQL: this.get('DATABASE_URL').startsWith('postgresql://') || this.get('DATABASE_URL').startsWith('postgres://')
    };
  }

  /**
   * Get JWT configuration
   */
  getJwtConfig() {
    return {
      secret: this.get('JWT_SECRET'),
      refreshSecret: this.get('JWT_REFRESH_SECRET'),
      expiresIn: this.isDevelopment() ? '24h' : '15m', // Longer expiry in development
      refreshExpiresIn: this.isDevelopment() ? '7d' : '7d'
    };
  }

  /**
   * Get session configuration
   */
  getSessionConfig() {
    return {
      secret: this.get('SESSION_SECRET'),
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: this.isProduction(), // HTTPS only in production
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: this.isProduction() ? 'strict' : 'lax'
      }
    };
  }

  /**
   * Get email configuration
   */
  getEmailConfig() {
    return {
      host: this.get('MAIL_HOST'),
      port: parseInt(this.get('MAIL_PORT')),
      secure: parseInt(this.get('MAIL_PORT')) === 465, // true for 465, false for other ports
      auth: {
        user: this.get('MAIL_USER'),
        pass: this.get('MAIL_PASSWORD')
      },
      from: this.get('MAIL_FROM')
    };
  }

  /**
   * Get Supabase configuration
   */
  getSupabaseConfig() {
    return {
      url: this.get('SUPABASE_URL'),
      key: this.get('SUPABASE_KEY')
    };
  }

  /**
   * Get CORS configuration
   */
  getCorsConfig() {
    const frontendUrl = this.get('FRONTEND_URL');
    
    return {
      origin: this.isDevelopment() 
        ? [frontendUrl, 'http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174']
        : [frontendUrl],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    };
  }

  /**
   * Get security configuration
   */
  getSecurityConfig() {
    return {
      rateLimiting: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: this.isDevelopment() ? 1000 : 100, // More lenient in development
        message: 'Too many requests from this IP, please try again later.'
      },
      helmet: {
        contentSecurityPolicy: this.isDevelopment() ? false : {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"]
          }
        },
        crossOriginEmbedderPolicy: false // Disable for development compatibility
      }
    };
  }

  /**
   * Log configuration summary (without sensitive data)
   */
  logConfigSummary() {
    console.log('\n📋 Configuration Summary:');
    console.log('========================');
    console.log(`Environment: ${this.get('NODE_ENV')}`);
    console.log(`Port: ${this.get('PORT')}`);
    console.log(`Database: ${this.getDatabaseConfig().isSQLite ? 'SQLite' : 'PostgreSQL'}`);
    console.log(`Fake Auth: ${this.isFakeAuthMode() ? 'Enabled' : 'Disabled'}`);
    console.log(`Frontend URL: ${this.get('FRONTEND_URL')}`);
    console.log(`Log Level: ${this.get('LOG_LEVEL')}`);
    
    if (this.validationResult.warnings.length > 0) {
      console.log(`Warnings: ${this.validationResult.warnings.length}`);
    }
    
    console.log('========================\n');
  }

  /**
   * Get all configuration (for debugging - be careful with sensitive data)
   */
  getAllConfig() {
    if (!this.isInitialized) {
      throw new Error('Configuration not initialized. Call initialize() first.');
    }

    // Return a copy to prevent modification
    return { ...this.config };
  }

  /**
   * Validate configuration at runtime
   */
  validateRuntime() {
    if (!this.isInitialized) {
      return false;
    }

    // Check if critical configuration is still valid
    const critical = ['JWT_SECRET', 'DATABASE_URL', 'PORT'];
    
    for (const key of critical) {
      if (!this.config[key]) {
        console.error(`❌ Critical configuration missing at runtime: ${key}`);
        return false;
      }
    }

    return true;
  }
}

// Export singleton instance
const config = new ConfigManager();

// Initialize configuration immediately
config.initialize();

export default config;
