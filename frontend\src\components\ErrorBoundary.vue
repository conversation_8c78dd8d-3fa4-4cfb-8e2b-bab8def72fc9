<template>
  <div v-if="hasError" class="error-boundary">
    <div class="min-h-screen flex items-center justify-center bg-gray-50">
      <div class="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div class="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
          <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        
        <div class="mt-4 text-center">
          <h3 class="text-lg font-medium text-gray-900">Something went wrong</h3>
          <p class="mt-2 text-sm text-gray-500">
            We're sorry, but something unexpected happened. Please try refreshing the page.
          </p>
          
          <div v-if="showDetails" class="mt-4 p-3 bg-gray-100 rounded text-left">
            <p class="text-xs text-gray-600 font-mono">{{ errorMessage }}</p>
          </div>
          
          <div class="mt-6 flex space-x-3">
            <button
              @click="refresh"
              class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Refresh Page
            </button>
            
            <button
              @click="toggleDetails"
              class="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              {{ showDetails ? 'Hide' : 'Show' }} Details
            </button>
          </div>
          
          <div class="mt-4">
            <button
              @click="goHome"
              class="text-sm text-blue-600 hover:text-blue-500"
            >
              Go to Homepage
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <slot v-else />
</template>

<script>
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'ErrorBoundary',
  setup() {
    const hasError = ref(false)
    const errorMessage = ref('')
    const showDetails = ref(false)
    const router = useRouter()

    onErrorCaptured((error, instance, info) => {
      console.error('ErrorBoundary caught error:', error)
      console.error('Component info:', info)
      
      hasError.value = true
      errorMessage.value = error.message || 'Unknown error occurred'
      
      // Return false to prevent the error from propagating further
      return false
    })

    const refresh = () => {
      window.location.reload()
    }

    const toggleDetails = () => {
      showDetails.value = !showDetails.value
    }

    const goHome = () => {
      hasError.value = false
      router.push('/')
    }

    const reset = () => {
      hasError.value = false
      errorMessage.value = ''
      showDetails.value = false
    }

    return {
      hasError,
      errorMessage,
      showDetails,
      refresh,
      toggleDetails,
      goHome,
      reset
    }
  }
}
</script>

<style scoped>
.error-boundary {
  /* Additional styles if needed */
}
</style>
