import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { Jim<PERSON> } from 'jimp';
import webp from 'webp-converter';
import fakeProductService from './fakeProductService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to get MIME type from file extension
function getMimeTypeFromExtension(ext) {
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff'
  };
  return mimeTypes[ext.toLowerCase()] || 'image/jpeg';
}

class ImageProcessingService {
  constructor() {
    this.uploadsDir = path.join(__dirname, '../../uploads');
    this.processedDir = path.join(__dirname, '../../uploads/processed');
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.uploadsDir, { recursive: true });
      await fs.mkdir(this.processedDir, { recursive: true });
    } catch (error) {
      console.error('Error creating directories:', error);
    }
  }

  // Resize image using Jimp
  async resizeImage(inputPath, outputPath, options = {}) {
    const startTime = Date.now();

    try {
      const { width, height, quality = 80 } = options;

      console.log(`Starting resize: ${inputPath} -> ${outputPath}`);
      console.log(`Dimensions: ${width}x${height}, Quality: ${quality}`);

      // Get input file stats
      const inputStats = await fs.stat(inputPath);

      // Load the image with Jimp (supports WebP, PNG, JPEG, BMP, TIFF, GIF)
      const image = await Jimp.read(inputPath);

      // Calculate dimensions
      let targetWidth = width ? parseInt(width) : image.bitmap.width;
      let targetHeight = height ? parseInt(height) : image.bitmap.height;

      // Maintain aspect ratio if only one dimension is provided
      if (width && !height) {
        targetHeight = Math.round((image.bitmap.height * targetWidth) / image.bitmap.width);
      } else if (height && !width) {
        targetWidth = Math.round((image.bitmap.width * targetHeight) / image.bitmap.height);
      }

      // Resize the image
      image.resize(targetWidth, targetHeight);

      // Set quality for JPEG output
      if (outputPath.toLowerCase().includes('.jpg') || outputPath.toLowerCase().includes('.jpeg')) {
        image.quality(quality);
      }

      // Save the processed image
      await image.write(outputPath);

      const processingTime = Date.now() - startTime;
      const outputStats = await fs.stat(outputPath);

      console.log(`Resize completed in ${processingTime}ms`);
      console.log(`File size: ${inputStats.size} -> ${outputStats.size} bytes`);

      return {
        success: true,
        processingTime,
        inputFileSize: inputStats.size,
        outputFileSize: outputStats.size,
        compressionRatio: inputStats.size / outputStats.size
      };
    } catch (error) {
      console.error('Error in resizeImage:', error);
      return {
        success: false,
        processingTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  // Add watermark to image using Jimp
  async addWatermark(inputPath, outputPath, options = {}) {
    const startTime = Date.now();

    try {
      const {
        text = 'WATERMARK',
        position = 'bottom-right',
        opacity = 0.7
      } = options;

      console.log(`Starting watermark: ${inputPath} -> ${outputPath}`);
      console.log(`Text: ${text}, Position: ${position}, Opacity: ${opacity}`);

      // Get input file stats
      const inputStats = await fs.stat(inputPath);

      // Load the image with Jimp
      const image = await Jimp.read(inputPath);

      // Load a font (Jimp has built-in fonts)
      const font = await Jimp.loadFont(Jimp.FONT_SANS_32_WHITE);

      // Calculate text position based on position parameter
      let x, y;
      const textWidth = Jimp.measureText(font, text);
      const textHeight = Jimp.measureTextHeight(font, text);

      switch (position) {
        case 'top-left':
          x = 20;
          y = 20;
          break;
        case 'top-right':
          x = image.bitmap.width - textWidth - 20;
          y = 20;
          break;
        case 'bottom-left':
          x = 20;
          y = image.bitmap.height - textHeight - 20;
          break;
        case 'bottom-right':
        default:
          x = image.bitmap.width - textWidth - 20;
          y = image.bitmap.height - textHeight - 20;
          break;
        case 'center':
          x = (image.bitmap.width - textWidth) / 2;
          y = (image.bitmap.height - textHeight) / 2;
          break;
      }

      // Add the watermark text
      image.print(font, x, y, text);

      // Apply opacity by creating a semi-transparent overlay
      if (opacity < 1) {
        // Create a clone for the watermark effect
        const watermarkImage = image.clone();
        image.composite(watermarkImage, 0, 0, {
          mode: Jimp.BLEND_MULTIPLY,
          opacitySource: opacity,
          opacityDest: 1
        });
      }

      // Save the watermarked image
      await image.write(outputPath);

      const processingTime = Date.now() - startTime;
      const outputStats = await fs.stat(outputPath);

      console.log(`Watermark completed in ${processingTime}ms`);

      return {
        success: true,
        processingTime,
        inputFileSize: inputStats.size,
        outputFileSize: outputStats.size,
        watermarkApplied: true
      };
    } catch (error) {
      console.error('Error in addWatermark:', error);
      return {
        success: false,
        processingTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  // Helper method to handle WebP conversions using webp-converter
  async convertWebP(inputPath, outputPath, options = {}) {
    const startTime = Date.now();

    try {
      const { quality = 80 } = options;
      const inputFormat = path.extname(inputPath).toLowerCase().slice(1);
      const outputFormat = path.extname(outputPath).toLowerCase().slice(1);

      console.log(`WebP conversion: ${inputPath} -> ${outputPath}`);
      console.log(`Input: ${inputFormat}, Output: ${outputFormat}, Quality: ${quality}`);

      // Get input file stats
      const inputStats = await fs.stat(inputPath);

      let result;

      if (outputFormat === 'webp') {
        // Convert to WebP using cwebp
        result = await webp.cwebp(inputPath, outputPath, `-q ${quality}`);
      } else if (inputFormat === 'webp') {
        // Convert from WebP using dwebp
        result = await webp.dwebp(inputPath, outputPath, '-o');
      } else {
        throw new Error('WebP conversion requires either input or output to be WebP format');
      }

      const processingTime = Date.now() - startTime;
      const outputStats = await fs.stat(outputPath);

      return {
        success: true,
        processingTime,
        inputFileSize: inputStats.size,
        outputFileSize: outputStats.size,
        format: outputFormat,
        compressionRatio: outputStats.size / inputStats.size
      };
    } catch (error) {
      return {
        success: false,
        processingTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  // Convert image format using Jimp with WebP fallback
  async convertFormat(inputPath, outputPath, options = {}) {
    const startTime = Date.now();

    try {
      const { quality = 80 } = options;
      const inputFormat = path.extname(inputPath).toLowerCase().slice(1);
      const outputFormat = path.extname(outputPath).toLowerCase().slice(1);

      console.log(`Starting format conversion: ${inputPath} -> ${outputPath}`);
      console.log(`Input: ${inputFormat}, Output: ${outputFormat}, Quality: ${quality}`);

      // Check if this is a WebP conversion
      if (inputFormat === 'webp' || outputFormat === 'webp') {
        console.log('Using WebP converter for WebP format');
        return await this.convertWebP(inputPath, outputPath, options);
      }

      // Get input file stats
      const inputStats = await fs.stat(inputPath);

      // Use Jimp for non-WebP conversions
      const image = await Jimp.read(inputPath);

      // Set quality for JPEG output
      if (outputFormat === 'jpg' || outputFormat === 'jpeg') {
        image.quality(quality);
      }

      // Jimp automatically handles format conversion based on file extension
      await image.write(outputPath);

      const processingTime = Date.now() - startTime;
      const outputStats = await fs.stat(outputPath);

      return {
        success: true,
        processingTime,
        inputFileSize: inputStats.size,
        outputFileSize: outputStats.size,
        format: outputFormat,
        compressionRatio: outputStats.size / inputStats.size
      };
    } catch (error) {
      return {
        success: false,
        processingTime: Date.now() - startTime,
        error: error.message
      };
    }
  }

  // Get image metadata (simplified implementation)
  async getImageMetadata(imagePath) {
    try {
      const stats = await fs.stat(imagePath);
      const ext = path.extname(imagePath).toLowerCase().slice(1);

      return {
        width: 800, // Mock dimensions
        height: 600,
        format: ext || 'unknown',
        size: stats.size,
        hasAlpha: ext === 'png',
        colorType: 'RGB'
      };
    } catch (error) {
      throw new Error(`Failed to get image metadata: ${error.message}`);
    }
  }

  // Process image with usage tracking
  async processImageWithTracking(userId, purchaseId, operation, inputPath, outputPath, options = {}) {
    let result;
    
    switch (operation) {
      case 'resize':
        result = await this.resizeImage(inputPath, outputPath, options);
        break;
      case 'watermark':
        result = await this.addWatermark(inputPath, outputPath, options);
        break;
      case 'convert':
        result = await this.convertFormat(inputPath, outputPath, options);
        break;
      default:
        throw new Error(`Unsupported operation: ${operation}`);
    }

    // Log usage
    const purchase = await fakeProductService.getPurchaseById(purchaseId);
    if (purchase) {
      await fakeProductService.logProductUsage({
        userId,
        productId: purchase.productId,
        purchaseId,
        action: operation,
        inputData: JSON.stringify(options),
        outputData: JSON.stringify(result),
        inputFileSize: result.inputFileSize,
        outputFileSize: result.outputFileSize,
        inputFormat: path.extname(inputPath).slice(1),
        outputFormat: path.extname(outputPath).slice(1),
        processingTime: result.processingTime,
        success: result.success,
        errorMessage: result.error || null,
        creditsUsed: 1
      });
    }

    return result;
  }

  // Helper methods

  // Generate unique filename
  generateFileName(originalName, suffix = '') {
    const ext = path.extname(originalName);
    const name = path.basename(originalName, ext);
    const timestamp = Date.now();
    return `${name}${suffix}_${timestamp}${ext}`;
  }

  // Clean up old files (call periodically)
  async cleanupOldFiles(maxAgeHours = 24) {
    try {
      const files = await fs.readdir(this.processedDir);
      const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
      
      for (const file of files) {
        const filePath = path.join(this.processedDir, file);
        const stats = await fs.stat(filePath);
        
        if (Date.now() - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          console.log(`Cleaned up old file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up old files:', error);
    }
  }
}

export default new ImageProcessingService();
