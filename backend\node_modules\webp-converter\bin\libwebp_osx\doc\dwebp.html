<!-- Creator     : groff version 1.19.2 -->
<!-- CreationDate: Thu Dec 26 09:17:31 2019 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p     { margin-top: 0; margin-bottom: 0; }
       pre   { margin-top: 0; margin-bottom: 0; }
       table { margin-top: 0; margin-bottom: 0; }
</style>
<title>DWEBP</title>

</head>
<body>

<h1 align=center>DWEBP</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#AUTHORS">AUTHORS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<a name="NAME"></a>
<h2>NAME</h2>


<p style="margin-left:11%; margin-top: 1em">dwebp &minus;
decompress a WebP file to an image file</p>

<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>


<p style="margin-left:11%; margin-top: 1em"><b>dwebp</b>
[<i>options</i>] <i>input_file.webp</i></p>

<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>


<p style="margin-left:11%; margin-top: 1em">This manual
page documents the <b>dwebp</b> command.</p>

<p style="margin-left:11%; margin-top: 1em"><b>dwebp</b>
decompresses WebP files into PNG, PAM, PPM or PGM
images.</p>

<a name="OPTIONS"></a>
<h2>OPTIONS</h2>


<p style="margin-left:11%; margin-top: 1em">The basic
options are:</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="3%">



<p style="margin-top: 1em" valign="top"><b>&minus;h</b></p> </td>
<td width="8%"></td>
<td width="30%">


<p style="margin-top: 1em" valign="top">Print usage
summary.</p> </td>
<td width="48%">
</td>
</table>

<p style="margin-left:11%;"><b>&minus;version</b></p>

<p style="margin-left:22%;">Print the version number (as
major.minor.revision) and exit.</p>

<p style="margin-left:11%;"><b>&minus;o</b>
<i>string</i></p>

<p style="margin-left:22%;">Specify the name of the output
file (as PNG format by default). Using &quot;-&quot; as
output name will direct output to &rsquo;stdout&rsquo;.</p>

<p style="margin-left:11%;"><b>&minus;&minus;</b>
<i>string</i></p>

<p style="margin-left:22%;">Explicitly specify the input
file. This option is useful if the input file starts with an
&rsquo;&minus;&rsquo; for instance. This option must appear
<b>last</b>. Any other options afterward will be ignored. If
the input file is &quot;&minus;&quot;, the data will be read
from <i>stdin</i> instead of a file.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;bmp</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to uncompressed BMP.</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;tiff</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to uncompressed TIFF.</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;pam</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to PAM (retains alpha).</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;ppm</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to PPM (discards alpha).</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;pgm</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to PGM. The output consists of luma/chroma samples
instead of RGB, using the IMC4 layout. This option is mainly
for verification and debugging purposes.</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;yuv</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Change the output
format to raw YUV. The output consists of
luma/chroma-U/chroma-V samples instead of RGB, saved
sequentially as individual planes. This option is mainly for
verification and debugging purposes.</p></td>
</table>

<p style="margin-left:11%;"><b>&minus;nofancy</b></p>

<p style="margin-left:22%;">Don&rsquo;t use the fancy
upscaler for YUV420. This may lead to jaggy edges
(especially the red ones), but should be faster.</p>

<p style="margin-left:11%;"><b>&minus;nofilter</b></p>

<p style="margin-left:22%;">Don&rsquo;t use the in-loop
filtering process even if it is required by the bitstream.
This may produce visible blocks on the non-compliant output,
but it will make the decoding faster.</p>

<p style="margin-left:11%;"><b>&minus;dither</b>
<i>strength</i></p>

<p style="margin-left:22%;">Specify a dithering
<b>strength</b> between 0 and 100. Dithering is a
post-processing effect applied to chroma components in lossy
compression. It helps by smoothing gradients and avoiding
banding artifacts.</p>

<p style="margin-left:11%;"><b>&minus;alpha_dither</b></p>

<p style="margin-left:22%;">If the compressed file contains
a transparency plane that was quantized during compression,
this flag will allow dithering the reconstructed plane in
order to generate smoother transparency gradients.</p>

<p style="margin-left:11%;"><b>&minus;nodither</b></p>

<p style="margin-left:22%;">Disable all dithering
(default).</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="4%">



<p style="margin-top: 1em" valign="top"><b>&minus;mt</b></p> </td>
<td width="7%"></td>
<td width="70%">


<p style="margin-top: 1em" valign="top">Use multi-threading
for decoding, if possible.</p></td>
<td width="8%">
</td>
</table>

<p style="margin-left:11%;"><b>&minus;crop</b>
<i>x_position y_position width height</i></p>

<p style="margin-left:22%;">Crop the decoded picture to a
rectangle with top-left corner at coordinates
(<b>x_position</b>, <b>y_position</b>) and size <b>width</b>
x <b>height</b>. This cropping area must be fully contained
within the source rectangle. The top-left corner will be
snapped to even coordinates if needed. This option is meant
to reduce the memory needed for cropping large images. Note:
the cropping is applied <i>before</i> any scaling.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="7%">



<p style="margin-top: 1em" valign="top"><b>&minus;flip</b></p> </td>
<td width="4%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Flip decoded image
vertically (can be useful for OpenGL textures for
instance).</p> </td>
</table>

<p style="margin-left:11%;"><b>&minus;resize</b>,
<b>&minus;scale</b> <i>width height</i></p>

<p style="margin-left:22%;">Rescale the decoded picture to
dimension <b>width</b> x <b>height</b>. This option is
mostly intended to reducing the memory needed to decode
large images, when only a small version is needed
(thumbnail, preview, etc.). Note: scaling is applied
<i>after</i> cropping. If either (but not both) of the
<b>width</b> or <b>height</b> parameters is 0, the value
will be calculated preserving the aspect-ratio.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;quiet</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Do not print
anything.</p> </td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;v</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Print extra
information (decoding time in particular).</p></td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;noasm</b></p> </td>
<td width="2%"></td>
<td width="78%">


<p style="margin-top: 1em" valign="top">Disable all
assembly optimizations.</p></td>
</table>

<a name="BUGS"></a>
<h2>BUGS</h2>


<p style="margin-left:11%; margin-top: 1em">Please report
all bugs to the issue tracker:
https://bugs.chromium.org/p/webp <br>
Patches welcome! See this page to get started:
http://www.webmproject.org/code/contribute/submitting-patches/</p>

<a name="EXAMPLES"></a>
<h2>EXAMPLES</h2>


<p style="margin-left:11%; margin-top: 1em">dwebp
picture.webp &minus;o output.png <br>
dwebp picture.webp &minus;ppm &minus;o output.ppm <br>
dwebp &minus;o output.ppm &minus;&minus;
&minus;&minus;&minus;picture.webp <br>
cat picture.webp | dwebp &minus;o &minus; &minus;&minus;
&minus; &gt; output.ppm</p>

<a name="AUTHORS"></a>
<h2>AUTHORS</h2>


<p style="margin-left:11%; margin-top: 1em"><b>dwebp</b> is
a part of libwebp and was written by the WebP team. <br>
The latest source tree is available at
https://chromium.googlesource.com/webm/libwebp</p>

<p style="margin-left:11%; margin-top: 1em">This manual
page was written by Pascal Massimino
&lt;<EMAIL>&gt;, for the Debian project
(and may be used by others).</p>

<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>



<p style="margin-left:11%; margin-top: 1em"><b>cwebp</b>(1),
<b>gif2webp</b>(1), <b>webpmux</b>(1) <br>
Please refer to http://developers.google.com/speed/webp/ for
additional information.</p>

<p style="margin-left:11%; margin-top: 1em"><b>Output file
format details</b> <br>
PAM: http://netpbm.sourceforge.net/doc/pam.html <br>
PGM: http://netpbm.sourceforge.net/doc/pgm.html <br>
PPM: http://netpbm.sourceforge.net/doc/ppm.html <br>
PNG: http://www.libpng.org/pub/png/png-sitemap.html#info</p>
<hr>
</body>
</html>
