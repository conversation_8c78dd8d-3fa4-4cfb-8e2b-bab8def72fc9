{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "node index.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "seed": "prisma db seed", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "canvas": "^3.1.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "nodemailer": "^6.9.11", "sharp": "^0.34.3"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "babel-jest": "^30.0.0-beta.3", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "jest": "^29.7.0", "jest-mock-extended": "^4.0.0-beta1", "pg": "^8.16.0", "prettier": "^3.5.3", "prisma": "^6.9.0", "supertest": "^7.1.1"}}