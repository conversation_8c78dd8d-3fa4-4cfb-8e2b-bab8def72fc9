<!-- Creator     : groff version 1.19.2 -->
<!-- CreationDate: Thu Dec 26 09:17:31 2019 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p     { margin-top: 0; margin-bottom: 0; }
       pre   { margin-top: 0; margin-bottom: 0; }
       table { margin-top: 0; margin-bottom: 0; }
</style>
<title>WEBPINFO</title>

</head>
<body>

<h1 align=center>WEBPINFO</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#OPTIONS">OPTIONS</a><br>
<a href="#INPUT">INPUT</a><br>
<a href="#BUGS">BUGS</a><br>
<a href="#EXAMPLES">EXAMPLES</a><br>
<a href="#AUTHORS">AUTHORS</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<a name="NAME"></a>
<h2>NAME</h2>


<p style="margin-left:11%; margin-top: 1em">webpinfo
&minus; print out the chunk level structure of WebP files
along with basic integrity checks.</p>

<a name="SYNOPSIS"></a>
<h2>SYNOPSIS</h2>



<p style="margin-left:11%; margin-top: 1em"><b>webpinfo</b>
<i>OPTIONS INPUT</i> <b><br>
webpinfo
[&minus;h|&minus;help|&minus;H|&minus;longhelp]</b></p>

<a name="DESCRIPTION"></a>
<h2>DESCRIPTION</h2>


<p style="margin-left:11%; margin-top: 1em">This manual
page documents the <b>webpinfo</b> command.</p>


<p style="margin-left:11%; margin-top: 1em"><b>webpinfo</b>
can be used to print out the chunk level structure and
bitstream header information of WebP files. It can also
check if the files are of valid WebP format.</p>

<a name="OPTIONS"></a>
<h2>OPTIONS</h2>



<p style="margin-left:11%; margin-top: 1em"><b>&minus;version</b></p>

<p style="margin-left:22%;">Print the version number (as
major.minor.revision) and exit.</p>

<table width="100%" border=0 rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;quiet</b></p> </td>
<td width="2%"></td>
<td width="58%">


<p style="margin-top: 1em" valign="top">Do not show chunk
parsing information.</p></td>
<td width="20%">
</td>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">



<p style="margin-top: 1em" valign="top"><b>&minus;diag</b></p> </td>
<td width="2%"></td>
<td width="58%">


<p style="margin-top: 1em" valign="top">Show parsing error
diagnosis.</p> </td>
<td width="20%">
</td>
</table>

<p style="margin-left:11%;"><b>&minus;summary</b></p>

<p style="margin-left:22%;">Show chunk stats summary.</p>


<p style="margin-left:11%;"><b>&minus;bitstream_info</b></p>

<p style="margin-left:22%;">Parse bitstream header.</p>

<p style="margin-left:11%;"><b>&minus;h,
&minus;help</b></p>

<p style="margin-left:22%;">A short usage summary.</p>

<p style="margin-left:11%;"><b>&minus;H,
&minus;longhelp</b></p>

<p style="margin-left:22%;">Detailed usage
instructions.</p>

<a name="INPUT"></a>
<h2>INPUT</h2>


<p style="margin-left:11%; margin-top: 1em">Input files in
WebP format. Input files must come last, following options
(if any). There can be multiple input files.</p>

<a name="BUGS"></a>
<h2>BUGS</h2>


<p style="margin-left:11%; margin-top: 1em">Please report
all bugs to the issue tracker:
https://bugs.chromium.org/p/webp <br>
Patches welcome! See this page to get started:
http://www.webmproject.org/code/contribute/submitting&minus;patches/</p>

<a name="EXAMPLES"></a>
<h2>EXAMPLES</h2>


<p style="margin-left:11%; margin-top: 1em">webpinfo
&minus;h <br>
webpinfo &minus;diag &minus;summary input_file.webp <br>
webpinfo &minus;bitstream_info input_file_1.webp
input_file_2.webp <br>
webpinfo *.webp</p>

<a name="AUTHORS"></a>
<h2>AUTHORS</h2>



<p style="margin-left:11%; margin-top: 1em"><b>webpinfo</b>
is a part of libwebp and was written by the WebP team. <br>
The latest source tree is available at
https://chromium.googlesource.com/webm/libwebp</p>

<p style="margin-left:11%; margin-top: 1em">This manual
page was written by Hui Su &lt;<EMAIL>&gt;, for the
Debian project (and may be used by others).</p>

<a name="SEE ALSO"></a>
<h2>SEE ALSO</h2>



<p style="margin-left:11%; margin-top: 1em"><b>webpmux</b>(1)
<br>
Please refer to http://developers.google.com/speed/webp/ for
additional information.</p>
<hr>
</body>
</html>
