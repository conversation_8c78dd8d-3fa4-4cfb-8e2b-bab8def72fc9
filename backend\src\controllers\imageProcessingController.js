import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { fileURLToPath } from 'url';
import imageProcessingService from '../services/imageProcessingService.js';
import fakeProductService from '../services/fakeProductService.js';

// Async error handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Helper function to clean up files with retry logic for Windows
async function cleanupFileWithRetry(filePath, maxRetries = 3, delay = 1000) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await fs.unlink(filePath);
      console.log(`File cleaned up successfully: ${filePath}`);
      return;
    } catch (error) {
      console.log(`Cleanup attempt ${attempt}/${maxRetries} failed for ${filePath}:`, error.message);

      if (attempt === maxRetries) {
        console.error(`Failed to cleanup file after ${maxRetries} attempts:`, filePath);
        // Don't throw error - just log it and continue
        return;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadsDir = path.join(__dirname, '../../uploads');
    await fs.mkdir(uploadsDir, { recursive: true });
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

// Enhanced security validation
const validateFileSecurely = (file) => {
  // Allowed MIME types (strict whitelist)
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff',
    'image/tif'
  ];

  // Allowed file extensions (strict whitelist)
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'];

  // Security checks
  const fileExtension = path.extname(file.originalname).toLowerCase();
  const fileName = path.basename(file.originalname, fileExtension);

  // 1. Check MIME type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    throw new Error(`Invalid MIME type: ${file.mimetype}. Only image files are allowed.`);
  }

  // 2. Check file extension
  if (!allowedExtensions.includes(fileExtension)) {
    throw new Error(`Invalid file extension: ${fileExtension}. Only image files are allowed.`);
  }

  // 3. Check filename for malicious patterns
  const maliciousPatterns = [
    /\.\./,           // Directory traversal
    /[<>:"|?*]/,      // Invalid filename characters
    /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i, // Windows reserved names
    /^\./,            // Hidden files
    /\.(exe|bat|cmd|scr|pif|com|dll|vbs|js|jar|php|asp|jsp|py|rb|pl)$/i // Executable extensions
  ];

  if (maliciousPatterns.some(pattern => pattern.test(file.originalname))) {
    throw new Error('Invalid filename. Potentially malicious file detected.');
  }

  // 4. Check filename length
  if (file.originalname.length > 255) {
    throw new Error('Filename too long. Maximum 255 characters allowed.');
  }

  // 5. Check if filename is empty or only whitespace
  if (!fileName.trim()) {
    throw new Error('Invalid filename. Filename cannot be empty.');
  }

  return true;
};

const fileFilter = (req, file, cb) => {
  console.log('File filter called with:', {
    fieldname: file.fieldname,
    originalname: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });

  try {
    validateFileSecurely(file);
    console.log('File accepted:', file.originalname);
    cb(null, true);
  } catch (error) {
    console.log('File rejected:', file.originalname, 'Reason:', error.message);
    cb(error, false);
  }
};

// Enhanced upload configuration with security limits
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 1,                   // Only allow 1 file at a time
    fields: 10,                 // Limit number of form fields
    fieldNameSize: 100,         // Limit field name size
    fieldSize: 1024 * 1024,     // 1MB limit for field values
    headerPairs: 2000           // Limit header pairs
  }
});

// Rate limiting storage (in production, use Redis or database)
const rateLimitStore = new Map();

// Rate limiting function
const checkRateLimit = (userId, operation = 'general') => {
  const key = `${userId}:${operation}`;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute window
  const maxRequests = 10; // Max 10 requests per minute per operation

  if (!rateLimitStore.has(key)) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  const record = rateLimitStore.get(key);

  if (now > record.resetTime) {
    // Reset the window
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxRequests) {
    return false;
  }

  record.count++;
  return true;
};

// Additional security validation for processed files
const validateProcessedFile = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);

    // Check if file size is reasonable (not too small or too large)
    if (stats.size < 100) { // Less than 100 bytes is suspicious
      throw new Error('Processed file is too small, possibly corrupted');
    }

    if (stats.size > 100 * 1024 * 1024) { // More than 100MB is suspicious
      throw new Error('Processed file is too large');
    }

    return true;
  } catch (error) {
    throw new Error(`File validation failed: ${error.message}`);
  }
};

// Check if user has active purchase for image processing
const checkImageProcessingAccess = async (userId) => {
  const purchases = await fakeProductService.getUserPurchases(userId, { status: 'ACTIVE' });
  const imageProcessingPurchases = purchases.filter(p => 
    p.product && p.product.category === 'IMAGE_PROCESSING'
  );
  
  if (imageProcessingPurchases.length === 0) {
    throw new Error('No active image processing service found. Please purchase a plan first.');
  }
  
  // Check usage limits
  const activePurchase = imageProcessingPurchases[0]; // Use first active purchase
  if (activePurchase.usageLimit && activePurchase.usageCount >= activePurchase.usageLimit) {
    throw new Error('Usage limit exceeded. Please upgrade your plan or purchase additional credits.');
  }
  
  return activePurchase;
};

// Resize image
export const resizeImage = asyncHandler(async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        message: 'No image file provided'
      });
    }

    const userId = req.user.userId;

    // Rate limiting check
    if (!checkRateLimit(userId, 'resize')) {
      return res.status(429).json({
        message: 'Rate limit exceeded. Please wait before making more requests.',
        retryAfter: 60
      });
    }

    const { width, height, fit = 'cover', quality = 80, format } = req.body;
    
    // Check access
    const purchase = await checkImageProcessingAccess(userId);
    
    // Enhanced parameter validation
    if (!width && !height) {
      return res.status(400).json({
        message: 'At least width or height must be specified'
      });
    }

    // Validate dimensions are reasonable
    const maxDimension = 8000; // 8K resolution limit
    const minDimension = 1;

    if (width && (width < minDimension || width > maxDimension)) {
      return res.status(400).json({
        message: `Width must be between ${minDimension} and ${maxDimension} pixels`
      });
    }

    if (height && (height < minDimension || height > maxDimension)) {
      return res.status(400).json({
        message: `Height must be between ${minDimension} and ${maxDimension} pixels`
      });
    }

    // Validate quality parameter
    const qualityNum = parseInt(quality);
    if (isNaN(qualityNum) || qualityNum < 1 || qualityNum > 100) {
      return res.status(400).json({
        message: 'Quality must be a number between 1 and 100'
      });
    }

    const inputPath = req.file.path;
    const outputFormat = format || path.extname(req.file.originalname).slice(1);
    const outputFileName = imageProcessingService.generateFileName(
      req.file.originalname, 
      `_resized_${width || 'auto'}x${height || 'auto'}`
    ).replace(path.extname(req.file.originalname), `.${outputFormat}`);
    const outputPath = path.join(imageProcessingService.processedDir, outputFileName);

    const result = await imageProcessingService.processImageWithTracking(
      userId,
      purchase.id,
      'resize',
      inputPath,
      outputPath,
      {
        width: width ? parseInt(width) : undefined,
        height: height ? parseInt(height) : undefined,
        fit,
        quality: parseInt(quality)
      }
    );

    if (!result.success) {
      return res.status(500).json({
        message: 'Image processing failed',
        error: result.error
      });
    }

    // Validate processed file
    try {
      await validateProcessedFile(outputPath);
    } catch (validationError) {
      console.error('Processed file validation failed:', validationError.message);
      // Clean up potentially malicious file
      try {
        await fs.unlink(outputPath);
      } catch (cleanupError) {
        console.error('Failed to cleanup invalid processed file:', cleanupError.message);
      }
      return res.status(500).json({
        message: 'Image processing validation failed',
        error: 'Processed file failed security validation'
      });
    }

    // Clean up input file with retry logic
    try {
      await cleanupFileWithRetry(inputPath);
    } catch (cleanupError) {
      console.error('Warning: Could not cleanup input file:', cleanupError.message);
      // Don't fail the request due to cleanup issues
    }

    res.json({
      message: 'Image resized successfully',
      data: {
        originalFile: req.file.originalname,
        processedFile: outputFileName,
        downloadUrl: `/downloads/${outputFileName}`,
        processingTime: result.processingTime,
        originalSize: result.inputFileSize,
        processedSize: result.outputFileSize,
        compressionRatio: result.compressionRatio,
        remainingCredits: purchase.usageLimit ? purchase.usageLimit - purchase.usageCount - 1 : 'unlimited'
      }
    });
  } catch (error) {
    console.error('Error resizing image:', error);
    console.error('Error stack:', error.stack);

    // Clean up uploaded file if it exists
    if (req.file) {
      try {
        await cleanupFileWithRetry(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError.message);
        // Don't fail the request due to cleanup issues
      }
    }

    res.status(500).json({
      message: 'Failed to resize image',
      error: error.message,
      details: error.stack
    });
  }
});

// Add watermark to image
export const addWatermark = asyncHandler(async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        message: 'No image file provided'
      });
    }

    const userId = req.user.userId;

    // Rate limiting check
    if (!checkRateLimit(userId, 'watermark')) {
      return res.status(429).json({
        message: 'Rate limit exceeded. Please wait before making more requests.',
        retryAfter: 60
      });
    }

    const {
      text,
      position = 'bottom-right',
      opacity = 0.7,
      fontSize = 24,
      color = 'white',
      fontFamily = 'Arial'
    } = req.body;
    
    // Check access
    const purchase = await checkImageProcessingAccess(userId);
    
    // Enhanced parameter validation
    if (!text) {
      return res.status(400).json({
        message: 'Watermark text is required'
      });
    }

    // Validate text length and content
    if (text.length > 200) {
      return res.status(400).json({
        message: 'Watermark text must be 200 characters or less'
      });
    }

    // Check for potentially malicious content in text
    const maliciousTextPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:text\/html/i
    ];

    if (maliciousTextPatterns.some(pattern => pattern.test(text))) {
      return res.status(400).json({
        message: 'Invalid watermark text content'
      });
    }

    // Validate numeric parameters
    const opacityNum = parseFloat(opacity);
    if (isNaN(opacityNum) || opacityNum < 0 || opacityNum > 1) {
      return res.status(400).json({
        message: 'Opacity must be a number between 0 and 1'
      });
    }

    const fontSizeNum = parseInt(fontSize);
    if (isNaN(fontSizeNum) || fontSizeNum < 8 || fontSizeNum > 200) {
      return res.status(400).json({
        message: 'Font size must be between 8 and 200 pixels'
      });
    }

    // Validate position
    const validPositions = ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'center'];
    if (!validPositions.includes(position)) {
      return res.status(400).json({
        message: `Position must be one of: ${validPositions.join(', ')}`
      });
    }

    const inputPath = req.file.path;
    const outputFileName = imageProcessingService.generateFileName(
      req.file.originalname, 
      '_watermarked'
    );
    const outputPath = path.join(imageProcessingService.processedDir, outputFileName);

    const result = await imageProcessingService.processImageWithTracking(
      userId,
      purchase.id,
      'watermark',
      inputPath,
      outputPath,
      {
        text,
        position,
        opacity: parseFloat(opacity),
        fontSize: parseInt(fontSize),
        color,
        fontFamily
      }
    );

    if (!result.success) {
      return res.status(500).json({
        message: 'Image processing failed',
        error: result.error
      });
    }

    // Clean up input file with retry logic
    try {
      await cleanupFileWithRetry(inputPath);
    } catch (cleanupError) {
      console.error('Warning: Could not cleanup input file:', cleanupError.message);
      // Don't fail the request due to cleanup issues
    }

    res.json({
      message: 'Watermark added successfully',
      data: {
        originalFile: req.file.originalname,
        processedFile: outputFileName,
        downloadUrl: `/downloads/${outputFileName}`,
        processingTime: result.processingTime,
        originalSize: result.inputFileSize,
        processedSize: result.outputFileSize,
        watermarkApplied: result.watermarkApplied,
        remainingCredits: purchase.usageLimit ? purchase.usageLimit - purchase.usageCount - 1 : 'unlimited'
      }
    });
  } catch (error) {
    console.error('Error adding watermark:', error);
    console.error('Error stack:', error.stack);

    // Clean up uploaded file if it exists
    if (req.file) {
      try {
        await cleanupFileWithRetry(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError.message);
        // Don't fail the request due to cleanup issues
      }
    }

    res.status(500).json({
      message: 'Failed to add watermark',
      error: error.message,
      details: error.stack
    });
  }
});

// Convert image format
export const convertFormat = asyncHandler(async (req, res) => {
  console.log('=== Convert Image Request Started ===');
  console.log('File:', req.file);
  console.log('Body:', req.body);
  console.log('User:', req.user);

  try {
    if (!req.file) {
      console.log('ERROR: No image file provided');
      return res.status(400).json({
        message: 'No image file provided'
      });
    }

    const userId = req.user.userId;

    // Rate limiting check
    if (!checkRateLimit(userId, 'convert')) {
      return res.status(429).json({
        message: 'Rate limit exceeded. Please wait before making more requests.',
        retryAfter: 60
      });
    }

    const { format, quality = 80 } = req.body;
    console.log('User ID:', userId);
    console.log('Format:', format, 'Quality:', quality);

    // Check access
    console.log('Checking image processing access...');
    const purchase = await checkImageProcessingAccess(userId);
    console.log('Purchase result:', purchase);

    if (!purchase) {
      console.log('ERROR: No purchase found for user');
      return res.status(403).json({
        message: 'You need to purchase the Image Processing service to use this feature'
      });
    }
    
    // Enhanced parameter validation
    if (!format) {
      return res.status(400).json({
        message: 'Target format is required'
      });
    }

    const supportedFormats = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'tiff'];
    if (!supportedFormats.includes(format.toLowerCase())) {
      return res.status(400).json({
        message: `Unsupported format. Supported formats: ${supportedFormats.join(', ')}`
      });
    }

    // Validate quality parameter
    const qualityNum = parseInt(quality);
    if (isNaN(qualityNum) || qualityNum < 1 || qualityNum > 100) {
      return res.status(400).json({
        message: 'Quality must be a number between 1 and 100'
      });
    }

    // Prevent conversion to same format (unless quality change is significant)
    const inputFormat = path.extname(req.file.originalname).slice(1).toLowerCase();
    if (inputFormat === format.toLowerCase() && Math.abs(qualityNum - 80) < 10) {
      return res.status(400).json({
        message: 'Converting to the same format with similar quality is not efficient'
      });
    }

    const inputPath = req.file.path;
    console.log('Input path:', inputPath);

    const outputFileName = imageProcessingService.generateFileName(
      req.file.originalname,
      `_converted`
    ).replace(path.extname(req.file.originalname), `.${format}`);
    const outputPath = path.join(imageProcessingService.processedDir, outputFileName);
    console.log('Output path:', outputPath);
    console.log('Output filename:', outputFileName);

    console.log('Starting image processing...');
    const result = await imageProcessingService.processImageWithTracking(
      userId,
      purchase.id,
      'convert',
      inputPath,
      outputPath,
      {
        quality: parseInt(quality)
      }
    );
    console.log('Processing result:', result);

    if (!result.success) {
      return res.status(500).json({
        message: 'Image processing failed',
        error: result.error
      });
    }

    // Clean up input file with retry logic
    try {
      await cleanupFileWithRetry(inputPath);
    } catch (cleanupError) {
      console.error('Warning: Could not cleanup input file:', cleanupError.message);
      // Don't fail the request due to cleanup issues
    }

    res.json({
      message: 'Image converted successfully',
      data: {
        originalFile: req.file.originalname,
        processedFile: outputFileName,
        downloadUrl: `/downloads/${outputFileName}`,
        processingTime: result.processingTime,
        originalSize: result.inputFileSize,
        processedSize: result.outputFileSize,
        originalFormat: path.extname(req.file.originalname).slice(1),
        convertedFormat: result.format,
        compressionRatio: result.compressionRatio,
        remainingCredits: purchase.usageLimit ? purchase.usageLimit - purchase.usageCount - 1 : 'unlimited'
      }
    });
  } catch (error) {
    console.error('Error converting image:', error);
    console.error('Error stack:', error.stack);

    // Clean up uploaded file if it exists
    if (req.file) {
      try {
        await cleanupFileWithRetry(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError.message);
        // Don't fail the request due to cleanup issues
      }
    }

    res.status(500).json({
      message: 'Failed to convert image',
      error: error.message,
      details: error.stack
    });
  }
});

// Download processed image
export const downloadImage = asyncHandler(async (req, res) => {
  try {
    console.log('Download request received for:', req.params.filename);
    const { filename } = req.params;

    // Security validation for filename
    if (!filename || typeof filename !== 'string') {
      return res.status(400).json({
        message: 'Invalid filename'
      });
    }

    // Prevent directory traversal attacks
    if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return res.status(400).json({
        message: 'Invalid filename: directory traversal not allowed'
      });
    }

    // Validate filename format (should be generated by our system)
    const validFilenamePattern = /^[a-zA-Z0-9_-]+\.(jpg|jpeg|png|webp|gif|tiff|bmp)$/;
    if (!validFilenamePattern.test(filename)) {
      return res.status(400).json({
        message: 'Invalid filename format'
      });
    }

    const filePath = path.join(imageProcessingService.processedDir, filename);

    // Ensure the resolved path is within the processed directory
    const resolvedPath = path.resolve(filePath);
    const processedDirResolved = path.resolve(imageProcessingService.processedDir);

    if (!resolvedPath.startsWith(processedDirResolved)) {
      return res.status(400).json({
        message: 'Access denied: file outside allowed directory'
      });
    }

    console.log('File path:', filePath);

    // Check if file exists
    try {
      await fs.access(filePath);
      console.log('File exists, sending download...');
    } catch (error) {
      console.log('File not found:', error.message);
      return res.status(404).json({
        message: 'File not found'
      });
    }

    res.download(filePath, filename);
  } catch (error) {
    console.error('Error downloading image:', error);
    res.status(500).json({
      message: 'Failed to download image',
      error: error.message
    });
  }
});

// Get image metadata
export const getImageMetadata = asyncHandler(async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        message: 'No image file provided'
      });
    }

    const userId = req.user.userId;

    // Rate limiting check
    if (!checkRateLimit(userId, 'metadata')) {
      return res.status(429).json({
        message: 'Rate limit exceeded. Please wait before making more requests.',
        retryAfter: 60
      });
    }

    const metadata = await imageProcessingService.getImageMetadata(req.file.path);
    
    // Clean up uploaded file with retry logic
    try {
      await cleanupFileWithRetry(req.file.path);
    } catch (cleanupError) {
      console.error('Warning: Could not cleanup uploaded file:', cleanupError.message);
      // Don't fail the request due to cleanup issues
    }
    
    res.json({
      message: 'Image metadata retrieved successfully',
      data: {
        filename: req.file.originalname,
        ...metadata
      }
    });
  } catch (error) {
    console.error('Error getting image metadata:', error);
    
    // Clean up uploaded file if it exists
    if (req.file) {
      try {
        await cleanupFileWithRetry(req.file.path);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError.message);
        // Don't fail the request due to cleanup issues
      }
    }
    
    res.status(500).json({
      message: 'Failed to get image metadata',
      error: error.message
    });
  }
});
