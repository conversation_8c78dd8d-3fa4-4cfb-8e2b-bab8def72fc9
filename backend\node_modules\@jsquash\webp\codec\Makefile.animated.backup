CODEC_URL = https://github.com/webmproject/libwebp/archive/d2e245ea9e959a5a79e1db0ed2085206947e98f2.tar.gz
CODEC_DIR = node_modules/libwebp
CODEC_BUILD_ROOT := $(CODEC_DIR)/build
# CODEC_DECODE_BUILD_DIR := $(CODEC_BUILD_ROOT)/decode
CODEC_ENCODE_BUILD_DIR := $(CODEC_BUILD_ROOT)/encode
# CODEC_SIMD_BUILD_DIR := $(CODEC_BUILD_ROOT)/simd
ENVIRONMENT = web,worker

PRE_JS = pre.js
OUT_JS = enc/webp_enc.js
# OUT_JS = enc/webp_enc.js enc/webp_enc_simd.js dec/webp_dec.js
OUT_WASM := $(OUT_JS:.js=.wasm)

.PHONY: all clean

all: $(OUT_JS)

# Define dependencies for all variations of build artifacts.
$(filter enc/%,$(OUT_JS)): enc/webp_enc.o
# $(filter dec/%,$(OUT_JS)): dec/webp_dec.o
# dec/webp_dec.js: $(CODEC_DECODE_BUILD_DIR)/libwebp.a $(CODEC_DECODE_BUILD_DIR)/libwebpdemux.a
enc/webp_enc.js: $(CODEC_ENCODE_BUILD_DIR)/libwebp.a $(CODEC_ENCODE_BUILD_DIR)/libwebpmux.a $(CODEC_ENCODE_BUILD_DIR)/libwebpdecoder.a
# enc/webp_enc_simd.js: $(CODEC_SIMD_BUILD_DIR)/libwebp.a $(CODEC_SIMD_BUILD_DIR)/libwebpmux.a

$(OUT_JS):
	$(LD) \
		$(LDFLAGS) \
		--pre-js $(PRE_JS) \
		--bind \
		-s ENVIRONMENT=$(ENVIRONMENT) \
		-s EXPORT_ES6=1 \
		-s DYNAMIC_EXECUTION=0 \
		-s MODULARIZE=1 \
		-o $@ \
		$+

%.o: %.cpp $(CODEC_DIR)/CMakeLists.txt
	$(CXX) -c \
		$(CXXFLAGS) \
		-I $(CODEC_DIR) \
		-o $@ \
		$<

# $(CODEC_DECODE_BUILD_DIR)/libwebp.a $(CODEC_DECODE_BUILD_DIR)/libwebpdemux.a: $(CODEC_DECODE_BUILD_DIR)/Makefile
# 	$(MAKE) -C $(CODEC_DECODE_BUILD_DIR)

$(CODEC_ENCODE_BUILD_DIR)/libwebp.a $(CODEC_ENCODE_BUILD_DIR)/libwebpmux.a $(CODEC_ENCODE_BUILD_DIR)/libwebpdemux.a $(CODEC_ENCODE_BUILD_DIR)/libwebpdecoder.a: $(CODEC_ENCODE_BUILD_DIR)/Makefile
	$(MAKE) -C $(CODEC_ENCODE_BUILD_DIR)

# $(CODEC_SIMD_BUILD_DIR)/libwebp.a $(CODEC_SIMD_BUILD_DIR)/libwebpmux.a: $(CODEC_SIMD_BUILD_DIR)/Makefile
# 	$(MAKE) -C $(CODEC_SIMD_BUILD_DIR)

# Enable demux on a decode build.
# $(CODEC_DECODE_BUILD_DIR)/Makefile: CMAKE_FLAGS+="-DWEBP_BUILD_DEMUX=1 -DWEBP_BUILD_WEBPMUX=0"

# Enable webpmux on an encode builds
# $(CODEC_ENCODE_BUILD_DIR)/Makefile $(CODEC_SIMD_BUILD_DIR)/Makefile: CMAKE_FLAGS+="-DWEBP_BUILD_DEMUX=9 -DWEBP_BUILD_WEBPMUX=1"

# # Enable SIMD on a SIMD build.
# $(CODEC_SIMD_BUILD_DIR)/Makefile: CMAKE_FLAGS+=-DWEBP_ENABLE_SIMD=1

%/Makefile: $(CODEC_DIR)/CMakeLists.txt
	emcmake cmake \
		$(CMAKE_FLAGS) \
		-DCMAKE_DISABLE_FIND_PACKAGE_Threads=1 \
		-DWEBP_BUILD_ANIM_UTILS=0 \
		-DWEBP_BUILD_CWEBP=0 \
		-DWEBP_BUILD_DWEBP=0 \
		-DWEBP_BUILD_GIF2WEBP=0 \
		-DWEBP_BUILD_IMG2WEBP=0 \
		-DWEBP_BUILD_VWEBP=0 \
		-DWEBP_BUILD_WEBPINFO=0 \
		-DWEBP_BUILD_EXTRAS=0 \
		-DWEBP_BUILD_DEMUX=1 \
		-DWEBP_BUILD_EXAMPLES=0 \
		-DWEBP_BUILD_WEBPMUX=1 \
		-B $(@D) \
		$(<D)

$(CODEC_DIR)/CMakeLists.txt:
	mkdir -p $(CODEC_DIR)
	curl -sL $(CODEC_URL) | tar xz --strip 1 -C $(CODEC_DIR)

clean:
	$(RM) $(OUT_JS) $(OUT_WASM)
	$(MAKE) -C $(CODEC_ENCODE_BUILD_DIR) clean
	$(MAKE) -C $(CODEC_DECODE_BUILD_DIR) clean
	$(MAKE) -C $(CODEC_SIMD_BUILD_DIR) clean
