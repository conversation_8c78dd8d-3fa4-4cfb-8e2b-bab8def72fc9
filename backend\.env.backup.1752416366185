##############################################
#               NODEJS-APP                   #
##############################################

# NODE_ENV determines the behavior (development, production, test)
NODE_ENV=development

# Port on which the Express server listens
PORT=3001

##############################################
#         DATABASE (Development Mode)       #
##############################################

# For development with fake auth, use SQLite
DATABASE_URL="file:./dev.db"

# Enable fake authentication mode (bypasses Supabase)
FAKE_AUTH_MODE=true

# Production Supabase settings (commented out for development)
#DATABASE_URL="postgresql://postgres.siepcixvaagyavfklioz:<EMAIL>:6543/postgres?pgbouncer=true"
#SUPABASE_URL=https://siepcixvaagyavfklioz.supabase.co
#SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNpZXBjaXh2YWFneWF2ZmtsaW96Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTAxMjMsImV4cCI6MjA2MjYyNjEyM30.SRwgwJl_WzHGyrsLTK9fiZPYKuAlzfDj5TudFPtSdoo

##############################################
#         SESSION & JWT / TOKEN-SETUP        #
##############################################

# SESSION_SECRET: Random, long string for signing session IDs (if session-based)
SESSION_SECRET=aVerySecretSessionSecretChangeMe

# JWT_SECRET: For signing access tokens
JWT_SECRET=aVerySecretJWTSecretChangeMe

# JWT_REFRESH_SECRET: For signing refresh tokens
JWT_REFRESH_SECRET=anotherSuperSecretRefreshTokenChangeMe

# FRONTEND_URL: URL of your Vue frontend (for redirects after login/logout)
FRONTEND_URL=http://localhost:5173

##############################################
#         SMTP / E-Mail-Versand (Nodemailer)  #
##############################################

# MAIL_HOST: Hostname of your SMTP server (e.g., "smtp.gmail.com")
MAIL_HOST=smtp.gmail.com

# MAIL_PORT: SMTP port (usually 587 for STARTTLS, 465 for SSL/TLS)
MAIL_PORT=587

# MAIL_USER: SMTP username
MAIL_USER=<EMAIL>

# MAIL_PASSWORD: SMTP password
MAIL_PASSWORD=your-app-password

# MAIL_FROM: Sender address for emails
MAIL_FROM="SaaSSy Platform <<EMAIL>>"

##############################################
#            LOGGING                         #
##############################################

# Optional: LOG_LEVEL (info, debug, warn, error)
LOG_LEVEL=info