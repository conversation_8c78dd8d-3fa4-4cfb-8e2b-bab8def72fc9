import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Async error handler wrapper to prevent unhandled promise rejections
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Static ESM imports (no dynamic `import()`)
import authRoutes from './src/routes/authRoutes.js';
 import userRoutes from './src/routes/userRoutes.js';
// import adminRoutes from './src/routes/adminRoutes.js';
import tileRoutes from './src/routes/tileRoutes.js';
import kbRoutes from './src/routes/kbRoutes.js'; // Added for Knowledge Base
import forumRoutes from './src/routes/forumRoutes.js'; // Added for Forum
import bookmarkRoutes from './src/routes/bookmarkRoutes.js'; // Added for Bookmarks
import followRoutes from './src/routes/followRoutes.js'; // Added for Follows
import reportRoutes from './src/routes/reportRoutes.js'; // Added for Reports
import adminActionRoutes from './src/routes/adminActionRoutes.js'; // Added for Admin Actions
import notificationRoutes from './src/routes/notificationRoutes.js'; // Added for Notifications
// import embeddingRoutes from './src/controllers/embeddingController.js';
 import planRoutes from './src/routes/planRoutes.js';
 import subscriptionRoutes from './src/routes/subscriptionRoutes.js';
 import creditRoutes from './src/routes/creditRoutes.js';
import productRoutes from './src/routes/productRoutes.js'; // Added for Products
import imageProcessingRoutes from './src/routes/imageProcessingRoutes.js'; // Added for Image Processing
import config from './src/config/index.js'; // Secure configuration manager

console.log('Starting server...');
console.log('Importing routes...');
console.log('Auth Routes:', authRoutes);
console.log('User Routes:', userRoutes);
//  console.log('Admin Routes:', adminRoutes);
console.log('Tile Routes:', tileRoutes);
console.log('Knowledge Base Routes:', kbRoutes); // Added for Knowledge Base
console.log('Forum Routes:', forumRoutes); // Added for Forum
console.log('Bookmark Routes:', bookmarkRoutes); // Added for Bookmarks
console.log('Follow Routes:', followRoutes); // Added for Follows
console.log('Report Routes:', reportRoutes); // Added for Reports
console.log('Admin Action Routes:', adminActionRoutes); // Added for Admin Actions
console.log('Notification Routes:', notificationRoutes); // Added for Notifications
// console.log('Embedding Routes:', embeddingRoutes);
 console.log('Plan Routes:', planRoutes);
 console.log('Subscription Routes:', subscriptionRoutes);
console.log('Credit Routes:', creditRoutes);
console.log('Product Routes:', productRoutes); // Added for Products
console.log('Image Processing Routes:', imageProcessingRoutes); // Added for Image Processing

const app = express();

// Use secure configuration
const PORT = config.get('PORT');
const corsConfig = config.getCorsConfig();

app.use(cors(corsConfig));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Static file serving for processed images (public access) - MUST BE FIRST
const staticPath = path.join(__dirname, 'uploads', 'processed');
console.log('Static path:', staticPath);
app.use('/downloads', express.static(staticPath, {
  dotfiles: 'deny',
  index: false,
  redirect: false
}));
console.log('Static file serving for images registered');

//Register routers
app.use('/api/auth', authRoutes);
console.log('Auth routes registered');

app.use('/api/users', userRoutes);
console.log('User routes registered', userRoutes);

//app.use('/api/admin', adminRoutes);
console.log('Admin routes registered');

app.use('/api/tiles', tileRoutes);
console.log('Tile routes registered');

app.use('/api/kb', kbRoutes); // Added for Knowledge Base
console.log('Knowledge Base routes registered');

app.use('/api/forum', forumRoutes); // Added for Forum
console.log('Forum routes registered');

app.use('/api/bookmarks', bookmarkRoutes); // Added for Bookmarks
console.log('Bookmark routes registered');

app.use('/api/follows', followRoutes); // Added for Follows
console.log('Follow routes registered');

app.use('/api/reports', reportRoutes); // Added for Reports
console.log('Report routes registered');

app.use('/api/admin/actions', adminActionRoutes); // Added for Admin Actions
console.log('Admin Action routes registered');

app.use('/api/notifications', notificationRoutes); // Added for Notifications
console.log('Notification routes registered');

// app.use('/api/embeddings', embeddingRoutes);
// console.log('Embedding routes registered');

app.use('/api/plans', planRoutes);
// console.log('Plan routes registered');

app.use('/api/subscriptions', subscriptionRoutes);
// console.log('Subscription routes registered');

app.use('/api/credits', creditRoutes);
// console.log('Credit routes registered');

// Static file serving already registered above

app.use('/api', productRoutes); // Added for Products
console.log('Product routes registered');

app.use('/api/images', imageProcessingRoutes); // Added for Image Processing
console.log('Image Processing routes registered');

app.get('/', (req, res) => {
  res.send('Hello from SaaS Backend!');
});

// Global error handler
app.use((err, req, res, next) => {
  console.error('=== Global Error Handler ===');
  console.error('Error:', err.message);
  console.error('Stack:', err.stack);
  console.error('Request URL:', req.url);
  console.error('Request Method:', req.method);
  console.error('Request Headers:', req.headers);

  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  // Check if response was already sent
  if (res.headersSent) {
    console.error('Headers already sent, delegating to default Express error handler');
    return next(err);
  }

  if (err.isOperational) {
    res.status(statusCode).json({
      message,
      error: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  } else {
    res.status(500).json({
      message: 'Internal Server Error',
      error: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
});

// Enhanced global error handlers
process.on('uncaughtException', (error) => {
  console.error('=== UNCAUGHT EXCEPTION ===');
  console.error('Error:', error.message);
  console.error('Stack:', error.stack);
  console.error('Time:', new Date().toISOString());

  // Log but don't exit - let PM2 handle restarts if needed
  // In production, you might want to exit gracefully
  if (process.env.NODE_ENV === 'production') {
    console.error('Exiting process due to uncaught exception in production');
    process.exit(1);
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('=== UNHANDLED PROMISE REJECTION ===');
  console.error('Reason:', reason);
  console.error('Promise:', promise);
  console.error('Time:', new Date().toISOString());

  // Log but don't exit - let PM2 handle restarts if needed
  if (config.isProduction()) {
    console.error('Exiting process due to unhandled rejection in production');
    process.exit(1);
  }
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  console.error('Stack:', reason?.stack);
  // Don't exit the process, just log the error
});

app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
