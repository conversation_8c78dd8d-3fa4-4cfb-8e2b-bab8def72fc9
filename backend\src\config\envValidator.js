import crypto from 'crypto';

/**
 * Environment Configuration Validator
 * Validates and secures environment variables with proper error handling
 */

class EnvironmentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.config = {};
  }

  /**
   * Validate a required environment variable
   */
  validateRequired(key, description, validator = null) {
    const value = process.env[key];
    
    if (!value || value.trim() === '') {
      this.errors.push(`Missing required environment variable: ${key} (${description})`);
      return null;
    }

    if (validator && !validator(value)) {
      this.errors.push(`Invalid value for ${key}: ${description}`);
      return null;
    }

    this.config[key] = value.trim();
    return value.trim();
  }

  /**
   * Validate an optional environment variable with default
   */
  validateOptional(key, defaultValue, description, validator = null) {
    const value = process.env[key];
    
    if (!value || value.trim() === '') {
      this.config[key] = defaultValue;
      return defaultValue;
    }

    if (validator && !validator(value)) {
      this.warnings.push(`Invalid value for ${key}, using default: ${defaultValue} (${description})`);
      this.config[key] = defaultValue;
      return defaultValue;
    }

    this.config[key] = value.trim();
    return value.trim();
  }

  /**
   * Validate port number
   */
  validatePort(value) {
    const port = parseInt(value);
    return !isNaN(port) && port > 0 && port <= 65535;
  }

  /**
   * Validate URL format
   */
  validateUrl(value) {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate email format
   */
  validateEmail(value) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  }

  /**
   * Validate JWT secret strength
   */
  validateJwtSecret(value) {
    // Should be at least 32 characters and not contain obvious patterns
    if (value.length < 32) return false;
    if (value.includes('ChangeMe') || value.includes('Secret')) return false;
    if (value === 'aVerySecretJWTSecretChangeMe') return false;
    return true;
  }

  /**
   * Validate session secret strength
   */
  validateSessionSecret(value) {
    // Should be at least 32 characters and not contain obvious patterns
    if (value.length < 32) return false;
    if (value.includes('ChangeMe') || value.includes('Secret')) return false;
    if (value === 'aVerySecretSessionSecretChangeMe') return false;
    return true;
  }

  /**
   * Validate database URL format
   */
  validateDatabaseUrl(value) {
    // Accept file:// for SQLite or postgresql:// for PostgreSQL
    return value.startsWith('file:') || value.startsWith('postgresql://') || value.startsWith('postgres://');
  }

  /**
   * Validate log level
   */
  validateLogLevel(value) {
    const validLevels = ['error', 'warn', 'info', 'debug'];
    return validLevels.includes(value.toLowerCase());
  }

  /**
   * Validate NODE_ENV
   */
  validateNodeEnv(value) {
    const validEnvs = ['development', 'production', 'test'];
    return validEnvs.includes(value.toLowerCase());
  }

  /**
   * Generate a secure random secret
   */
  generateSecureSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Main validation function
   */
  validate() {
    console.log('🔒 Validating environment configuration...');

    // Core application settings
    this.validateRequired('NODE_ENV', 'Application environment (development/production/test)', this.validateNodeEnv);
    this.validateRequired('PORT', 'Server port number', this.validatePort);

    // Database configuration
    this.validateRequired('DATABASE_URL', 'Database connection string', this.validateDatabaseUrl);

    // Authentication mode
    const fakeAuthMode = this.validateOptional('FAKE_AUTH_MODE', 'false', 'Enable fake authentication for development');
    
    // JWT and session secrets
    const jwtSecret = this.validateRequired('JWT_SECRET', 'JWT signing secret', this.validateJwtSecret);
    const jwtRefreshSecret = this.validateRequired('JWT_REFRESH_SECRET', 'JWT refresh token secret', this.validateJwtSecret);
    const sessionSecret = this.validateRequired('SESSION_SECRET', 'Session signing secret', this.validateSessionSecret);

    // Check if secrets are unique
    if (jwtSecret && jwtRefreshSecret && jwtSecret === jwtRefreshSecret) {
      this.errors.push('JWT_SECRET and JWT_REFRESH_SECRET must be different');
    }

    // Frontend URL
    this.validateRequired('FRONTEND_URL', 'Frontend application URL', this.validateUrl);

    // Email configuration (optional in development)
    if (process.env.NODE_ENV === 'production') {
      this.validateRequired('MAIL_HOST', 'SMTP server hostname');
      this.validateRequired('MAIL_PORT', 'SMTP server port', this.validatePort);
      this.validateRequired('MAIL_USER', 'SMTP username', this.validateEmail);
      this.validateRequired('MAIL_PASSWORD', 'SMTP password');
      this.validateRequired('MAIL_FROM', 'Email sender address');
    } else {
      this.validateOptional('MAIL_HOST', 'smtp.gmail.com', 'SMTP server hostname');
      this.validateOptional('MAIL_PORT', '587', 'SMTP server port', this.validatePort);
      this.validateOptional('MAIL_USER', '<EMAIL>', 'SMTP username');
      this.validateOptional('MAIL_PASSWORD', 'your-app-password', 'SMTP password');
      this.validateOptional('MAIL_FROM', '"SaaSSy Platform <<EMAIL>>"', 'Email sender address');
    }

    // Logging
    this.validateOptional('LOG_LEVEL', 'info', 'Logging level', this.validateLogLevel);

    // Supabase configuration (only required in production without fake auth)
    if (process.env.NODE_ENV === 'production' && fakeAuthMode !== 'true') {
      this.validateRequired('SUPABASE_URL', 'Supabase project URL', this.validateUrl);
      this.validateRequired('SUPABASE_KEY', 'Supabase anon key');
    }

    // Security warnings for development
    if (process.env.NODE_ENV === 'development') {
      if (jwtSecret && jwtSecret.includes('ChangeMe')) {
        this.warnings.push('JWT_SECRET contains "ChangeMe" - consider using a more secure secret even in development');
      }
      if (sessionSecret && sessionSecret.includes('ChangeMe')) {
        this.warnings.push('SESSION_SECRET contains "ChangeMe" - consider using a more secure secret even in development');
      }
    }

    // Report results
    this.reportResults();

    return {
      isValid: this.errors.length === 0,
      config: this.config,
      errors: this.errors,
      warnings: this.warnings
    };
  }

  /**
   * Report validation results
   */
  reportResults() {
    if (this.errors.length > 0) {
      console.error('❌ Environment validation failed:');
      this.errors.forEach(error => console.error(`   • ${error}`));
    }

    if (this.warnings.length > 0) {
      console.warn('⚠️  Environment validation warnings:');
      this.warnings.forEach(warning => console.warn(`   • ${warning}`));
    }

    if (this.errors.length === 0) {
      console.log('✅ Environment validation passed');
      if (this.warnings.length === 0) {
        console.log('   No warnings detected');
      }
    }
  }

  /**
   * Generate secure secrets for development
   */
  generateDevelopmentSecrets() {
    console.log('\n🔑 Generated secure secrets for development:');
    console.log('Add these to your .env file:\n');
    
    console.log(`JWT_SECRET=${this.generateSecureSecret()}`);
    console.log(`JWT_REFRESH_SECRET=${this.generateSecureSecret()}`);
    console.log(`SESSION_SECRET=${this.generateSecureSecret()}`);
    
    console.log('\n⚠️  Keep these secrets secure and never commit them to version control!');
  }
}

// Export singleton instance
const envValidator = new EnvironmentValidator();

export default envValidator;
