{"users": [{"id": "user-1", "email": "<EMAIL>", "passwordHash": "$2b$10$dummy.hash.for.password123", "name": "Admin User", "role": "ADMIN", "isActive": true, "isEmailVerified": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "user-2", "email": "<EMAIL>", "passwordHash": "$2b$10$dummy.hash.for.password123", "name": "Editor User", "role": "EDITOR", "isActive": true, "isEmailVerified": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "user-3", "email": "<EMAIL>", "passwordHash": "$2b$10$dummy.hash.for.password123", "name": "Moderator User", "role": "MODERATOR", "isActive": true, "isEmailVerified": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "user-4", "email": "<EMAIL>", "passwordHash": "$2b$10$dummy.hash.for.password123", "name": "Customer User", "role": "CUSTOMER", "isActive": true, "isEmailVerified": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "user-5", "email": "<EMAIL>", "passwordHash": "$2b$10$dummy.hash.for.password123", "name": "Demo User", "role": "CUSTOMER", "isActive": true, "isEmailVerified": true, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "forumCategories": [{"id": "cat-1", "name": "General Discussion", "slug": "general-discussion", "description": "General topics and discussions", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "cat-2", "name": "Feature Requests", "slug": "feature-requests", "description": "Suggest new features and improvements", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "cat-3", "name": "Bug Reports", "slug": "bug-reports", "description": "Report bugs and technical issues", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "cat-4", "name": "Help & Support", "slug": "help-support", "description": "Get help with using the platform", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "forumThreads": [{"id": "thread-1", "title": "Welcome to SaaSSy Platform!", "slug": "welcome-to-saassy-platform", "content": "Welcome to our community forum! This is a place to discuss features, ask questions, and connect with other users.", "authorId": "user-1", "categoryId": "cat-1", "isPinned": true, "isLocked": false, "viewCount": 42, "tags": ["welcome", "announcement"], "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "thread-2", "title": "How to create your first knowledge base article", "slug": "how-to-create-first-kb-article", "content": "This guide will walk you through creating your first knowledge base article. Start by navigating to the Knowledge Base section...", "authorId": "user-2", "categoryId": "cat-4", "isPinned": false, "isLocked": false, "viewCount": 28, "tags": ["guide", "knowledge-base", "tutorial"], "createdAt": "2024-01-02T00:00:00.000Z", "updatedAt": "2024-01-02T00:00:00.000Z"}, {"id": "thread-1752342904809", "title": "Test Thread", "content": "This is a test thread created via API", "categoryId": "cat-1", "authorId": "user-1", "slug": "test-thread-1752342904809", "tags": "[\"test\",\"api\"]", "viewCount": 0, "isPinned": false, "isLocked": false, "createdAt": "2025-07-12T17:55:04.809Z", "updatedAt": "2025-07-12T17:55:04.809Z"}], "forumPosts": [{"id": "post-1", "content": "Thanks for the warm welcome! Looking forward to contributing to the community.", "authorId": "user-4", "threadId": "thread-1", "parentId": null, "createdAt": "2024-01-01T01:00:00.000Z", "updatedAt": "2024-01-01T01:00:00.000Z"}, {"id": "post-2", "content": "Great guide! This really helped me get started with the knowledge base feature.", "authorId": "user-5", "threadId": "thread-2", "parentId": null, "createdAt": "2024-01-02T01:00:00.000Z", "updatedAt": "2024-01-02T01:00:00.000Z"}], "kbCategories": [{"id": "kb-cat-1", "name": "Getting Started", "slug": "getting-started", "description": "Essential guides for new users", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "kb-cat-2", "name": "Advanced Features", "slug": "advanced-features", "description": "In-depth guides for power users", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "kb-cat-3", "name": "API Documentation", "slug": "api-documentation", "description": "Technical documentation for developers", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "kbArticles": [{"id": "article-1", "title": "Platform Overview", "slug": "platform-overview", "content": "# Platform Overview\n\nSaaSSy is a comprehensive platform that combines knowledge management with community features...", "excerpt": "Learn about the core features and capabilities of the SaaSSy platform.", "authorId": "user-2", "categoryId": "kb-cat-1", "status": "PUBLISHED", "tags": ["overview", "introduction"], "viewCount": 156, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "author": {"id": "user-2", "name": "Editor User", "email": "<EMAIL>"}}, {"id": "article-2", "title": "User Management Guide", "slug": "user-management-guide", "content": "# User Management Guide\n\nThis guide covers how to manage users, roles, and permissions...", "excerpt": "Complete guide to managing users and permissions in your organization.", "authorId": "user-1", "categoryId": "kb-cat-2", "status": "PUBLISHED", "tags": ["users", "permissions", "admin"], "viewCount": 89, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "author": {"id": "user-1", "name": "Admin User", "email": "<EMAIL>"}}], "notifications": [{"id": "notif-1", "userId": "user-4", "type": "FORUM_REPLY", "title": "New reply to your post", "message": "Someone replied to your post in 'Welcome to SaaSSy Platform!'", "isRead": false, "createdAt": "2024-01-01T02:00:00.000Z"}, {"id": "notif-2", "userId": "user-5", "type": "ARTICLE_PUBLISHED", "title": "New article published", "message": "A new article 'User Management Guide' has been published", "isRead": false, "createdAt": "2024-01-01T01:30:00.000Z"}], "products": [{"id": "product-1", "name": "ImageCloud Pro", "slug": "imagecloud-pro", "description": "Professional image processing service with resize, watermark, and format conversion capabilities. Perfect for businesses and content creators who need reliable, fast image processing.", "shortDescription": "Professional image processing with resize, watermark, and conversion", "category": "IMAGE_PROCESSING", "type": "SERVICE", "status": "ACTIVE", "basePrice": 999, "currency": "USD", "billingType": "ONE_TIME", "features": "[\"Resize images to any dimension\", \"Add custom watermarks\", \"Convert between formats (JPG, PNG, WebP, GIF)\", \"Batch processing\", \"API access\", \"Cloud storage integration\", \"EXIF data preservation\", \"Quality optimization\"]", "limits": "{\"monthlyProcessing\": 1000, \"maxFileSize\": \"50MB\", \"supportedFormats\": [\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\", \"bmp\", \"tiff\"]}", "imageUrl": "/images/products/imagecloud-pro.jpg", "galleryImages": "[\"/images/products/imagecloud-gallery-1.jpg\", \"/images/products/imagecloud-gallery-2.jpg\", \"/images/products/imagecloud-gallery-3.jpg\"]", "metaTitle": "ImageCloud Pro - Professional Image Processing Service", "metaDescription": "Transform your images with our professional processing service. Resize, watermark, and convert images with ease.", "tags": "[\"image-processing\", \"resize\", \"watermark\", \"conversion\", \"professional\", \"api\"]", "isDigital": true, "requiresSetup": false, "setupInstructions": null, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "product-2", "name": "ImageCloud Starter", "slug": "imagecloud-starter", "description": "Basic image processing service perfect for personal use and small projects. Includes essential features like resize and format conversion.", "shortDescription": "Basic image processing for personal use", "category": "IMAGE_PROCESSING", "type": "SERVICE", "status": "ACTIVE", "basePrice": 299, "currency": "USD", "billingType": "ONE_TIME", "features": "[\"Resize images\", \"Basic format conversion\", \"Simple watermarks\", \"Web interface\"]", "limits": "{\"monthlyProcessing\": 100, \"maxFileSize\": \"10MB\", \"supportedFormats\": [\"jpg\", \"jpeg\", \"png\", \"gif\"]}", "imageUrl": "/images/products/imagecloud-starter.jpg", "galleryImages": "[\"/images/products/imagecloud-starter-gallery-1.jpg\"]", "metaTitle": "ImageCloud Starter - Basic Image Processing", "metaDescription": "Get started with image processing. Perfect for personal projects and small businesses.", "tags": "[\"image-processing\", \"starter\", \"basic\", \"personal\"]", "isDigital": true, "requiresSetup": false, "setupInstructions": null, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, {"id": "product-3", "name": "ImageCloud Enterprise", "slug": "imagecloud-enterprise", "description": "Enterprise-grade image processing solution with unlimited processing, advanced features, and dedicated support. Perfect for large organizations and high-volume applications.", "shortDescription": "Enterprise image processing with unlimited usage", "category": "IMAGE_PROCESSING", "type": "SUBSCRIPTION", "status": "ACTIVE", "basePrice": 4999, "currency": "USD", "billingType": "MONTHLY", "features": "[\"Unlimited image processing\", \"Advanced AI-powered optimization\", \"Custom watermark templates\", \"Bulk processing API\", \"Priority support\", \"Custom integrations\", \"Advanced analytics\", \"White-label options\"]", "limits": "{\"monthlyProcessing\": -1, \"maxFileSize\": \"500MB\", \"supportedFormats\": [\"jpg\", \"jpeg\", \"png\", \"gif\", \"webp\", \"bmp\", \"tiff\", \"raw\", \"svg\"]}", "imageUrl": "/images/products/imagecloud-enterprise.jpg", "galleryImages": "[\"/images/products/imagecloud-enterprise-gallery-1.jpg\", \"/images/products/imagecloud-enterprise-gallery-2.jpg\"]", "metaTitle": "ImageCloud Enterprise - Unlimited Image Processing", "metaDescription": "Enterprise-grade image processing with unlimited usage and advanced features.", "tags": "[\"image-processing\", \"enterprise\", \"unlimited\", \"api\", \"bulk\"]", "isDigital": true, "requiresSetup": true, "setupInstructions": "Our team will contact you within 24 hours to set up your enterprise account and provide API credentials.", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "productPurchases": [{"id": "purchase-1", "userId": "user-4", "productId": "product-1", "purchasePrice": 999, "currency": "USD", "status": "ACTIVE", "billingType": "ONE_TIME", "billingPeriod": null, "expiresAt": null, "usageCount": 67, "usageLimit": 1000, "lastUsedAt": "2025-07-13T14:23:15.839Z", "activatedAt": "2024-01-05T10:00:00.000Z", "activationKey": "IMG-PRO-2024-001", "paymentId": "pay_fake_12345", "paymentMethod": "credit_card", "createdAt": "2024-01-05T10:00:00.000Z", "updatedAt": "2025-07-13T14:23:15.839Z"}, {"id": "purchase-2", "userId": "user-5", "productId": "product-2", "purchasePrice": 299, "currency": "USD", "status": "ACTIVE", "billingType": "ONE_TIME", "billingPeriod": null, "expiresAt": null, "usageCount": 30, "usageLimit": 100, "lastUsedAt": "2025-07-13T13:54:51.510Z", "activatedAt": "2024-01-03T15:30:00.000Z", "activationKey": "IMG-START-2024-001", "paymentId": "pay_fake_67890", "paymentMethod": "paypal", "createdAt": "2024-01-03T15:30:00.000Z", "updatedAt": "2025-07-13T13:54:51.510Z"}, {"id": "purchase-1752346033210", "userId": "user-4", "productId": "product-2", "purchasePrice": 299, "currency": "USD", "status": "ACTIVE", "billingType": "ONE_TIME", "billingPeriod": null, "expiresAt": null, "usageCount": 0, "usageLimit": 100, "lastUsedAt": null, "activatedAt": "2025-07-12T18:47:13.210Z", "activationKey": "IMAGECLOUD-STARTER-1752346033210", "paymentId": "pay_fake_1752346033210", "paymentMethod": "credit_card", "createdAt": "2025-07-12T18:47:13.210Z", "updatedAt": "2025-07-12T18:47:13.210Z"}, {"id": "purchase-1752349790613", "userId": "user-1752346230060", "productId": "product-1", "purchasePrice": 999, "currency": "USD", "status": "ACTIVE", "billingType": "ONE_TIME", "billingPeriod": null, "expiresAt": null, "usageCount": 5, "usageLimit": null, "lastUsedAt": "2025-07-12T19:56:55.184Z", "activatedAt": "2025-07-12T19:49:50.613Z", "activationKey": "IMAGECLOUD-PRO-1752349790613", "paymentId": "pay_fake_1752349790613", "paymentMethod": "credit_card", "createdAt": "2025-07-12T19:49:50.613Z", "updatedAt": "2025-07-12T19:56:55.184Z"}, {"id": "purchase-1752349944864", "userId": "user-1752346230060", "productId": "product-3", "purchasePrice": 4999, "currency": "USD", "status": "ACTIVE", "billingType": "MONTHLY", "billingPeriod": null, "expiresAt": null, "usageCount": 0, "usageLimit": null, "lastUsedAt": null, "activatedAt": "2025-07-12T19:52:24.864Z", "activationKey": "IMAGECLOUD-ENTERPRISE-1752349944864", "paymentId": "pay_fake_1752349944864", "paymentMethod": "credit_card", "createdAt": "2025-07-12T19:52:24.864Z", "updatedAt": "2025-07-12T19:52:24.864Z"}, {"id": "purchase-1752350099345", "userId": "user-5", "productId": "product-1", "purchasePrice": 999, "currency": "USD", "status": "ACTIVE", "billingType": "ONE_TIME", "billingPeriod": null, "expiresAt": null, "usageCount": 0, "usageLimit": null, "lastUsedAt": null, "activatedAt": "2025-07-12T19:54:59.345Z", "activationKey": "IMAGECLOUD-PRO-1752350099345", "paymentId": "pay_fake_1752350099345", "paymentMethod": "credit_card", "createdAt": "2025-07-12T19:54:59.345Z", "updatedAt": "2025-07-12T19:54:59.345Z"}], "productUsageLogs": [{"id": "usage-1", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "resize", "inputData": "{\"width\": 800, \"height\": 600, \"format\": \"jpg\", \"quality\": 85}", "outputData": "{\"width\": 800, \"height\": 600, \"format\": \"jpg\", \"fileSize\": 245760}", "inputFileSize": 1048576, "outputFileSize": 245760, "inputFormat": "png", "outputFormat": "jpg", "processingTime": 1250, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2024-01-10T14:30:00.000Z"}, {"id": "usage-2", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "watermark", "inputData": "{\"text\": \"© My Company\", \"position\": \"bottom-right\", \"opacity\": 0.7}", "outputData": "{\"watermarkApplied\": true, \"fileSize\": 512000}", "inputFileSize": 500000, "outputFileSize": 512000, "inputFormat": "jpg", "outputFormat": "jpg", "processingTime": 890, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2024-01-10T13:15:00.000Z"}, {"id": "usage-3", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"targetFormat\": \"webp\", \"quality\": 80}", "outputData": "{\"format\": \"webp\", \"fileSize\": 156000, \"compressionRatio\": 0.65}", "inputFileSize": 240000, "outputFileSize": 156000, "inputFormat": "png", "outputFormat": "webp", "processingTime": 650, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2024-01-08T09:15:00.000Z"}, {"id": "usage-1752349852328", "userId": "user-1752346230060", "productId": "product-1", "purchaseId": "purchase-1752349790613", "action": "watermark", "inputData": "{\"text\":\"<PERSON>\",\"position\":\"bottom-right\",\"opacity\":0.7,\"fontSize\":24,\"color\":\"#ffffff\",\"fontFamily\":\"Arial\"}", "outputData": "{\"success\":true,\"processingTime\":877,\"inputFileSize\":2487540,\"outputFileSize\":2972610,\"watermarkApplied\":true}", "inputFileSize": 2487540, "outputFileSize": 2972610, "inputFormat": "png", "outputFormat": "png", "processingTime": 877, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T19:50:52.328Z"}, {"id": "usage-1752349877414", "userId": "user-1752346230060", "productId": "product-1", "purchaseId": "purchase-1752349790613", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":25,\"inputFileSize\":2487540,\"outputFileSize\":307544,\"format\":\"jpg\",\"compressionRatio\":0.12363379081341405}", "inputFileSize": 2487540, "outputFileSize": 307544, "inputFormat": "png", "outputFormat": "jpg", "processingTime": 25, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T19:51:17.414Z"}, {"id": "usage-1752349884552", "userId": "user-1752346230060", "productId": "product-1", "purchaseId": "purchase-1752349790613", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":140,\"inputFileSize\":2487540,\"outputFileSize\":314758,\"format\":\"webp\",\"compressionRatio\":0.12653384468189458}", "inputFileSize": 2487540, "outputFileSize": 314758, "inputFormat": "png", "outputFormat": "webp", "processingTime": 140, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T19:51:24.552Z"}, {"id": "usage-1752349992318", "userId": "user-1752346230060", "productId": "product-1", "purchaseId": "purchase-1752349790613", "action": "resize", "inputData": "{\"width\":1000,\"height\":1000,\"fit\":\"cover\",\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":462,\"inputFileSize\":2487540,\"outputFileSize\":323301,\"compressionRatio\":0.12996816131599895}", "inputFileSize": 2487540, "outputFileSize": 323301, "inputFormat": "png", "outputFormat": "png", "processingTime": 462, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T19:53:12.318Z"}, {"id": "usage-1752350215184", "userId": "user-1752346230060", "productId": "product-1", "purchaseId": "purchase-1752349790613", "action": "watermark", "inputData": "{\"text\":\"hgfjhgf\",\"position\":\"bottom-right\",\"opacity\":0.7,\"fontSize\":24,\"color\":\"#ffffff\",\"fontFamily\":\"Arial\"}", "outputData": "{\"success\":true,\"processingTime\":104,\"inputFileSize\":2487540,\"outputFileSize\":2972113,\"watermarkApplied\":true}", "inputFileSize": 2487540, "outputFileSize": 2972113, "inputFormat": "png", "outputFormat": "png", "processingTime": 104, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T19:56:55.184Z"}, {"id": "usage-1752350445401", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "resize", "inputData": "{\"width\":200,\"height\":200,\"fit\":\"cover\",\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":14,\"inputFileSize\":393630,\"outputFileSize\":2145,\"compressionRatio\":0.005449279780504535}", "inputFileSize": 393630, "outputFileSize": 2145, "inputFormat": "jpg", "outputFormat": "jpg", "processingTime": 14, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T20:00:45.401Z"}, {"id": "usage-1752352780898", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":137,\"inputFileSize\":2487540,\"outputFileSize\":314758,\"format\":\"webp\",\"compressionRatio\":0.12653384468189458}", "inputFileSize": 2487540, "outputFileSize": 314758, "inputFormat": "png", "outputFormat": "webp", "processingTime": 137, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T20:39:40.898Z"}, {"id": "usage-1752352839667", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":376,\"inputFileSize\":2487540,\"outputFileSize\":341416,\"format\":\"png\",\"compressionRatio\":0.13725045627406995}", "inputFileSize": 2487540, "outputFileSize": 341416, "inputFormat": "png", "outputFormat": "png", "processingTime": 376, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T20:40:39.667Z"}, {"id": "usage-1752353077206", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":23,\"inputFileSize\":2487540,\"outputFileSize\":307544,\"format\":\"jpg\",\"compressionRatio\":0.12363379081341405}", "inputFileSize": 2487540, "outputFileSize": 307544, "inputFormat": "png", "outputFormat": "jpg", "processingTime": 23, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T20:44:37.206Z"}, {"id": "usage-1752353262578", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":142,\"inputFileSize\":2487540,\"outputFileSize\":314758,\"format\":\"webp\",\"compressionRatio\":0.12653384468189458}", "inputFileSize": 2487540, "outputFileSize": 314758, "inputFormat": "png", "outputFormat": "webp", "processingTime": 142, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T20:47:42.578Z"}, {"id": "usage-1752353381825", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":304,\"error\":\"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SaaSSy\\\\backend\\\\uploads\\\\processed\\\\DALLÂ·E 2024-12-11 18.11.35 - A minimalist and professional 16_9 abstract header image for a modern business. The design features sleek geometric shapes, soft gradients of blue and_converted_1752353381521.png: unable to open for write\\nsystem error: No such file or directory\\nC:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\SaaSSy\\\\backend\\\\uploads\\\\processed\\\\DALLÂ·E 2024-12-11 18.11.35 - A minimalist and professional 16_9 abstract header image for a modern business. The design features sleek geometric shapes, soft gradients of blue and_converted_1752353381521.png: write error\\nsystem error: No such file or directory\\npngsave: stream error\\nwbuffer_write: write failed\\nsystem error: Unknown error\\nwbuffer_write: write failed\\nsystem error: Unknown error\"}", "inputFormat": "webp", "outputFormat": "png", "processingTime": 304, "success": false, "errorMessage": "C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\uploads\\processed\\DALLÂ·E 2024-12-11 18.11.35 - A minimalist and professional 16_9 abstract header image for a modern business. The design features sleek geometric shapes, soft gradients of blue and_converted_1752353381521.png: unable to open for write\nsystem error: No such file or directory\nC:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\uploads\\processed\\DALLÂ·E 2024-12-11 18.11.35 - A minimalist and professional 16_9 abstract header image for a modern business. The design features sleek geometric shapes, soft gradients of blue and_converted_1752353381521.png: write error\nsystem error: No such file or directory\npngsave: stream error\nwbuffer_write: write failed\nsystem error: Unknown error\nwbuffer_write: write failed\nsystem error: Unknown error", "creditsUsed": 1, "createdAt": "2025-07-12T20:49:41.825Z"}, {"id": "usage-1752355877047", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":387,\"inputFileSize\":2487540,\"outputFileSize\":341416,\"format\":\"png\",\"compressionRatio\":0.13725045627406995}", "inputFileSize": 2487540, "outputFileSize": 341416, "inputFormat": "png", "outputFormat": "png", "processingTime": 387, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T21:31:17.047Z"}, {"id": "usage-1752356211635", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "resize", "inputData": "{\"width\":200,\"height\":200,\"fit\":\"cover\",\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":56,\"inputFileSize\":2487540,\"outputFileSize\":11408,\"compressionRatio\":0.004586056907627616}", "inputFileSize": 2487540, "outputFileSize": 11408, "inputFormat": "png", "outputFormat": "png", "processingTime": 56, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T21:36:51.635Z"}, {"id": "usage-1752356230255", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "resize", "inputData": "{\"width\":200,\"height\":200,\"fit\":\"cover\",\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":9,\"inputFileSize\":39539,\"outputFileSize\":3395,\"compressionRatio\":0.08586458939275146}", "inputFileSize": 39539, "outputFileSize": 3395, "inputFormat": "jpg", "outputFormat": "jpg", "processingTime": 9, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T21:37:10.255Z"}, {"id": "usage-1752356243669", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "watermark", "inputData": "{\"text\":\"76587658\",\"position\":\"bottom-right\",\"opacity\":0.7,\"fontSize\":24,\"color\":\"#ffffff\",\"fontFamily\":\"Arial\"}", "outputData": "{\"success\":true,\"processingTime\":32,\"inputFileSize\":39539,\"outputFileSize\":26077,\"watermarkApplied\":true}", "inputFileSize": 39539, "outputFileSize": 26077, "inputFormat": "jpg", "outputFormat": "jpg", "processingTime": 32, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-12T21:37:23.669Z"}, {"id": "usage-1752399947173", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":167,\"inputFileSize\":47812,\"outputFileSize\":81183,\"format\":\"png\",\"compressionRatio\":1.6979628545135113}", "inputFileSize": 47812, "outputFileSize": 81183, "inputFormat": "JPG", "outputFormat": "png", "processingTime": 167, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T09:45:47.173Z"}, {"id": "usage-1752400917137", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":166,\"inputFileSize\":47812,\"outputFileSize\":81183,\"format\":\"png\",\"compressionRatio\":1.6979628545135113}", "inputFileSize": 47812, "outputFileSize": 81183, "inputFormat": "JPG", "outputFormat": "png", "processingTime": 166, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:01:57.137Z"}, {"id": "usage-1752400934419", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":180,\"inputFileSize\":116919,\"outputFileSize\":89154,\"format\":\"png\",\"compressionRatio\":0.7625279039334925}", "inputFileSize": 116919, "outputFileSize": 89154, "inputFormat": "JPG", "outputFormat": "png", "processingTime": 180, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:02:14.419Z"}, {"id": "usage-1752401044677", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":167,\"inputFileSize\":47812,\"outputFileSize\":81183,\"format\":\"png\",\"compressionRatio\":1.6979628545135113}", "inputFileSize": 47812, "outputFileSize": 81183, "inputFormat": "JPG", "outputFormat": "png", "processingTime": 167, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:04:04.677Z"}, {"id": "usage-1752401445696", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":56,\"inputFileSize\":47812,\"outputFileSize\":24018,\"format\":\"webp\",\"compressionRatio\":0.502342508156948}", "inputFileSize": 47812, "outputFileSize": 24018, "inputFormat": "JPG", "outputFormat": "webp", "processingTime": 56, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:10:45.696Z"}, {"id": "usage-1752401486303", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":54,\"inputFileSize\":47812,\"outputFileSize\":24018,\"format\":\"webp\",\"compressionRatio\":0.502342508156948}", "inputFileSize": 47812, "outputFileSize": 24018, "inputFormat": "JPG", "outputFormat": "webp", "processingTime": 54, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:11:26.303Z"}, {"id": "usage-1752401554366", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":491,\"inputFileSize\":116919,\"outputFileSize\":232340,\"format\":\"gif\",\"compressionRatio\":1.9871877111504546}", "inputFileSize": 116919, "outputFileSize": 232340, "inputFormat": "JPG", "outputFormat": "gif", "processingTime": 491, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:12:34.366Z"}, {"id": "usage-1752401717618", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":495,\"inputFileSize\":116919,\"outputFileSize\":232340,\"format\":\"gif\",\"compressionRatio\":1.9871877111504546}", "inputFileSize": 116919, "outputFileSize": 232340, "inputFormat": "JPG", "outputFormat": "gif", "processingTime": 495, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:15:17.618Z"}, {"id": "usage-1752401731463", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":81,\"inputFileSize\":116919,\"outputFileSize\":64540,\"format\":\"webp\",\"compressionRatio\":0.****************}", "inputFileSize": 116919, "outputFileSize": 64540, "inputFormat": "JPG", "outputFormat": "webp", "processingTime": 81, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T10:15:31.463Z"}, {"id": "usage-1752412878916", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":99,\"inputFileSize\":116919,\"outputFileSize\":64540,\"format\":\"webp\",\"compressionRatio\":0.****************}", "inputFileSize": 116919, "outputFileSize": 64540, "inputFormat": "JPG", "outputFormat": "webp", "processingTime": 99, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T13:21:18.916Z"}, {"id": "usage-1752412894657", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":55,\"inputFileSize\":47812,\"outputFileSize\":24018,\"format\":\"webp\",\"compressionRatio\":0.502342508156948}", "inputFileSize": 47812, "outputFileSize": 24018, "inputFormat": "JPG", "outputFormat": "webp", "processingTime": 55, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T13:21:34.657Z"}, {"id": "usage-1752414302245", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":1,\"error\":\"Unsupported image type\"}", "inputFormat": "webp", "outputFormat": "jpg", "processingTime": 1, "success": false, "errorMessage": "Unsupported image type", "creditsUsed": 1, "createdAt": "2025-07-13T13:45:02.245Z"}, {"id": "usage-1752414420636", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":2,\"error\":\"webp.dwebp is not a function\"}", "inputFormat": "webp", "outputFormat": "jpg", "processingTime": 2, "success": false, "errorMessage": "webp.dwebp is not a function", "creditsUsed": 1, "createdAt": "2025-07-13T13:47:00.636Z"}, {"id": "usage-1752414457390", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":1,\"error\":\"Unsupported image type\"}", "inputFormat": "webp", "outputFormat": "jpg", "processingTime": 1, "success": false, "errorMessage": "Unsupported image type", "creditsUsed": 1, "createdAt": "2025-07-13T13:47:37.390Z"}, {"id": "usage-1752414467794", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":459,\"inputFileSize\":2487540,\"outputFileSize\":312142,\"format\":\"jpg\",\"compressionRatio\":0.12548220330125345}", "inputFileSize": 2487540, "outputFileSize": 312142, "inputFormat": "png", "outputFormat": "jpg", "processingTime": 459, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T13:47:47.794Z"}, {"id": "usage-1752414478697", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "resize", "inputData": "{\"width\":800,\"height\":600,\"fit\":\"cover\",\"quality\":90}", "outputData": "{\"success\":true,\"processingTime\":49,\"inputFileSize\":2487540,\"outputFileSize\":188871,\"compressionRatio\":13.170576742856236}", "inputFileSize": 2487540, "outputFileSize": 188871, "inputFormat": "png", "outputFormat": "png", "processingTime": 49, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T13:47:58.697Z"}, {"id": "usage-1752414739454", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":true,\"processingTime\":111,\"inputFileSize\":116919,\"outputFileSize\":541792,\"format\":\"png\",\"compressionRatio\":4.633908945509284}", "inputFileSize": 116919, "outputFileSize": 541792, "inputFormat": "JPG", "outputFormat": "png", "processingTime": 111, "success": true, "errorMessage": null, "creditsUsed": 1, "createdAt": "2025-07-13T13:52:19.454Z"}, {"id": "usage-1752414891510", "userId": "user-5", "productId": "product-2", "purchaseId": "purchase-2", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":3,\"error\":\"Mime type image/webp does not support decoding\"}", "inputFormat": "webp", "outputFormat": "jpg", "processingTime": 3, "success": false, "errorMessage": "Mime type image/webp does not support decoding", "creditsUsed": 1, "createdAt": "2025-07-13T13:54:51.510Z"}, {"id": "usage-1752416595839", "userId": "user-4", "productId": "product-1", "purchaseId": "purchase-1", "action": "convert", "inputData": "{\"quality\":80}", "outputData": "{\"success\":false,\"processingTime\":142,\"error\":\"image.writeAsync is not a function\"}", "inputFormat": "JPG", "outputFormat": "png", "processingTime": 142, "success": false, "errorMessage": "image.writeAsync is not a function", "creditsUsed": 1, "createdAt": "2025-07-13T14:23:15.839Z"}], "productReviews": [{"id": "review-1", "userId": "user-4", "productId": "product-1", "rating": 5, "title": "Excellent image processing service!", "content": "ImageCloud Pro has been a game-changer for our business. The API is easy to use, processing is fast, and the quality is outstanding. Highly recommended!", "isVerified": true, "createdAt": "2024-01-08T16:45:00.000Z", "updatedAt": "2024-01-08T16:45:00.000Z"}, {"id": "review-2", "userId": "user-5", "productId": "product-2", "rating": 4, "title": "Great for personal projects", "content": "Perfect for my photography blog. Easy to use and does exactly what I need. Would love to see more format options in the future.", "isVerified": true, "createdAt": "2024-01-06T11:20:00.000Z", "updatedAt": "2024-01-06T11:20:00.000Z"}], "settings": {"notifications": {"emailEnabled": true, "inAppEnabled": true, "digestFrequency": "daily"}, "platform": {"name": "SaaSSy Platform", "description": "A comprehensive SaaS platform with knowledge management and community features", "version": "1.0.0"}}}