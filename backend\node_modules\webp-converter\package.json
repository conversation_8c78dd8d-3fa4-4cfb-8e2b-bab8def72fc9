{"name": "webp-converter", "version": "2.3.3", "description": "A small node.js library for converting any image to webp file format or converting webp image to any image file format.", "main": "src/webpconverter.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node app.js"}, "repository": {"type": "git", "url": "https://github.com/scionoftech/webp-converter.git"}, "keywords": ["webp", "webpconverter", "webp-converter", "cwebp", "dwebp", "gif2webp", "webpmux"], "files": ["LICENSE", "README.md", "app.js", "webpconverter.js", "src/buffer_utils.js", "src/temp_path.js", "src/cwebp.js", "src/dwebp.js", "src/gwebp.js", "src/webpmux.js", "bin/", "images/", "temp/"], "dependencies": {"uuid": "^8.3.2"}, "author": "scionoftech", "license": "MIT"}