#!/usr/bin/env node

/**
 * Generate Secure Secrets Script
 * Generates cryptographically secure secrets for development and production
 */

import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SecretGenerator {
  constructor() {
    this.secrets = {};
  }

  /**
   * Generate a cryptographically secure random string
   */
  generateSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate all required secrets
   */
  generateAllSecrets() {
    console.log('🔐 Generating secure secrets...\n');

    this.secrets = {
      JWT_SECRET: this.generateSecret(64),
      JWT_REFRESH_SECRET: this.generateSecret(64),
      SESSION_SECRET: this.generateSecret(64)
    };

    return this.secrets;
  }

  /**
   * Display secrets for manual copying
   */
  displaySecrets() {
    console.log('📋 Generated Secrets (copy to your .env file):');
    console.log('================================================\n');

    for (const [key, value] of Object.entries(this.secrets)) {
      console.log(`${key}=${value}`);
    }

    console.log('\n================================================');
    console.log('⚠️  SECURITY WARNINGS:');
    console.log('• Keep these secrets secure and never commit them to version control');
    console.log('• Use different secrets for development, staging, and production');
    console.log('• Rotate secrets regularly in production environments');
    console.log('• Store production secrets in a secure secret management system');
    console.log('================================================\n');
  }

  /**
   * Create a backup of current .env file
   */
  async backupEnvFile() {
    const envPath = path.join(__dirname, '..', '.env');
    const backupPath = path.join(__dirname, '..', `.env.backup.${Date.now()}`);

    try {
      await fs.access(envPath);
      await fs.copyFile(envPath, backupPath);
      console.log(`✅ Backed up existing .env to: ${path.basename(backupPath)}`);
      return true;
    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log('ℹ️  No existing .env file found, creating new one');
        return false;
      }
      throw error;
    }
  }

  /**
   * Update .env file with new secrets
   */
  async updateEnvFile() {
    const envPath = path.join(__dirname, '..', '.env');
    
    try {
      // Read existing .env file
      let envContent = '';
      try {
        envContent = await fs.readFile(envPath, 'utf8');
      } catch (error) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }

      // Update or add secrets
      let updatedContent = envContent;
      let hasChanges = false;

      for (const [key, value] of Object.entries(this.secrets)) {
        const regex = new RegExp(`^${key}=.*$`, 'm');
        const newLine = `${key}=${value}`;

        if (regex.test(updatedContent)) {
          // Update existing secret
          updatedContent = updatedContent.replace(regex, newLine);
          hasChanges = true;
        } else {
          // Add new secret
          if (updatedContent && !updatedContent.endsWith('\n')) {
            updatedContent += '\n';
          }
          updatedContent += `${newLine}\n`;
          hasChanges = true;
        }
      }

      if (hasChanges) {
        await fs.writeFile(envPath, updatedContent);
        console.log('✅ Updated .env file with new secrets');
      } else {
        console.log('ℹ️  No changes needed in .env file');
      }

    } catch (error) {
      console.error('❌ Failed to update .env file:', error.message);
      throw error;
    }
  }

  /**
   * Validate existing secrets in .env file
   */
  async validateExistingSecrets() {
    const envPath = path.join(__dirname, '..', '.env');
    
    try {
      const envContent = await fs.readFile(envPath, 'utf8');
      const lines = envContent.split('\n');
      const secrets = {};
      
      for (const line of lines) {
        const [key, value] = line.split('=');
        if (key && value) {
          secrets[key.trim()] = value.trim();
        }
      }

      console.log('🔍 Validating existing secrets...\n');

      const requiredSecrets = ['JWT_SECRET', 'JWT_REFRESH_SECRET', 'SESSION_SECRET'];
      const issues = [];

      for (const secretKey of requiredSecrets) {
        const secret = secrets[secretKey];
        
        if (!secret) {
          issues.push(`❌ Missing: ${secretKey}`);
          continue;
        }

        // Check for weak secrets
        if (secret.length < 32) {
          issues.push(`⚠️  Weak: ${secretKey} is too short (${secret.length} chars, minimum 32)`);
        }

        if (secret.includes('ChangeMe') || secret.includes('Secret')) {
          issues.push(`⚠️  Insecure: ${secretKey} contains obvious patterns`);
        }

        if (secret === 'aVerySecretJWTSecretChangeMe' || 
            secret === 'anotherSuperSecretRefreshTokenChangeMe' ||
            secret === 'aVerySecretSessionSecretChangeMe') {
          issues.push(`❌ Default: ${secretKey} is using default value`);
        }
      }

      // Check for duplicate secrets
      const secretValues = requiredSecrets.map(key => secrets[key]).filter(Boolean);
      const uniqueValues = new Set(secretValues);
      
      if (secretValues.length !== uniqueValues.size) {
        issues.push('❌ Duplicate secrets detected - all secrets must be unique');
      }

      if (issues.length === 0) {
        console.log('✅ All secrets are secure and properly configured\n');
        return true;
      } else {
        console.log('Issues found:');
        issues.forEach(issue => console.log(`   ${issue}`));
        console.log('');
        return false;
      }

    } catch (error) {
      if (error.code === 'ENOENT') {
        console.log('❌ No .env file found\n');
        return false;
      }
      throw error;
    }
  }

  /**
   * Interactive mode
   */
  async interactive() {
    console.log('🔐 SaaSSy Secret Generator');
    console.log('==========================\n');

    // Check existing secrets
    const isValid = await this.validateExistingSecrets();

    if (isValid) {
      console.log('Your secrets are already secure. No action needed.');
      return;
    }

    console.log('Generating new secure secrets...\n');

    // Generate new secrets
    this.generateAllSecrets();

    // Ask user what to do
    console.log('Choose an option:');
    console.log('1. Display secrets for manual copying');
    console.log('2. Automatically update .env file (with backup)');
    console.log('3. Both\n');

    // For this script, we'll default to option 3 (both)
    const choice = '3';

    switch (choice) {
      case '1':
        this.displaySecrets();
        break;
      case '2':
        await this.backupEnvFile();
        await this.updateEnvFile();
        break;
      case '3':
      default:
        await this.backupEnvFile();
        await this.updateEnvFile();
        console.log('');
        this.displaySecrets();
        break;
    }
  }
}

// Main execution
async function main() {
  try {
    const generator = new SecretGenerator();
    await generator.interactive();
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export default SecretGenerator;
