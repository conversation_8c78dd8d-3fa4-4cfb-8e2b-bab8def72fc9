import z from "zod";
declare const WebpOptionsSchema: z.ZodObject<{
    colorSpace: z.ZodOptional<z.ZodUnion<[z.ZodLiteral<"display-p3">, z.Zod<PERSON>iteral<"srgb">]>>;
    /**
     * Image quality, between 0 and 100.
     * For lossy, 0 gives the smallest size and 100 the largest.
     * For lossless, this parameter is the amount of effort put
     * into the compression: 0 is the fastest but gives larger
     * files compared to the slowest, but best, 100.
     * @default 100
     */
    quality: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** If non-zero, set the desired target size in bytes. */
    targetSize: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** If non-zero, specifies the minimal distortion to try to achieve. Takes precedence over target_size. */
    targetPSNR: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Quality/speed trade-off (0 = fast, 6 = slower-better). */
    method: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Spatial Noise Shaping. 0 = off, 100 = maximum. */
    snsStrength: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Range: 0 = off, 100 = strongest. */
    filterStrength: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Range: 0 = off, 7 = least sharp. */
    filterSharpness: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Filtering type: 0 = simple, 1 = strong (only used if filter_strength > 0 or autofilter > 0). */
    filterType: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** log2(number of token partitions) in 0..3. Default is set to 0 for easier progressive decoding. */
    partitions: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Maximum number of segments to use, in 1..4. */
    segments: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Number of entropy-analysis passes (in 1..10). */
    pass: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** If true, export the compressed picture back. In-loop filtering is not applied. */
    showCompressed: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** Preprocessing filter (0 = none, 1 = segment-smooth). */
    preprocessing: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** Auto adjust filter's strength (0 = off, 1 = on). */
    autoFilter: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** Quality degradation allowed to fit the 512k limit on prediction modes coding (0 = no degradation, 100 = maximum possible degradation). */
    partitionLimit: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Algorithm for encoding the alpha plane (0 = none, 1 = compressed with WebP lossless). */
    alphaCompression: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** Predictive filtering method for alpha plane (0 = none, 1 = fast, 2 = best). */
    alphaFiltering: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<0>, z.ZodLiteral<1>, z.ZodLiteral<2>]>>>;
    /** Between 0 (smallest size) and 100 (lossless). */
    alphaQuality: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Set to 1 for lossless encoding (default is lossy). */
    lossless: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** By default, RGB values in transparent areas will be modified to improve compression. Set exact to 1 to prevent this. */
    exact: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** If true, compression parameters will be remapped to better match the expected output size from JPEG compression. Generally, the output size will be similar but the degradation will be lower. */
    emulateJpegSize: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** If non-zero, try and use multi-threaded encoding. */
    threadLevel: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Reduce memory usage (slower encoding). */
    lowMemory: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** Near lossless encoding (0 = max loss, 100 = off). */
    nearLossless: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    /** Reserved for future lossless feature. */
    useDeltaPalette: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
    /** If needed, use sharp (and slow) RGB->YUV conversion. */
    useSharpYuv: z.ZodDefault<z.ZodOptional<z.ZodUnion<[z.ZodLiteral<1>, z.ZodLiteral<0>]>>>;
}, "strip", z.ZodTypeAny, {
    exact: 0 | 1;
    quality: number;
    targetSize: number;
    targetPSNR: number;
    method: number;
    snsStrength: number;
    filterStrength: number;
    filterSharpness: number;
    filterType: number;
    partitions: number;
    segments: number;
    pass: number;
    showCompressed: 0 | 1;
    preprocessing: 0 | 1;
    autoFilter: 0 | 1;
    partitionLimit: number;
    alphaCompression: 0 | 1;
    alphaFiltering: 0 | 2 | 1;
    alphaQuality: number;
    lossless: 0 | 1;
    emulateJpegSize: 0 | 1;
    threadLevel: number;
    lowMemory: 0 | 1;
    nearLossless: number;
    useDeltaPalette: 0 | 1;
    useSharpYuv: 0 | 1;
    colorSpace?: "display-p3" | "srgb" | undefined;
}, {
    exact?: 0 | 1 | undefined;
    colorSpace?: "display-p3" | "srgb" | undefined;
    quality?: number | undefined;
    targetSize?: number | undefined;
    targetPSNR?: number | undefined;
    method?: number | undefined;
    snsStrength?: number | undefined;
    filterStrength?: number | undefined;
    filterSharpness?: number | undefined;
    filterType?: number | undefined;
    partitions?: number | undefined;
    segments?: number | undefined;
    pass?: number | undefined;
    showCompressed?: 0 | 1 | undefined;
    preprocessing?: 0 | 1 | undefined;
    autoFilter?: 0 | 1 | undefined;
    partitionLimit?: number | undefined;
    alphaCompression?: 0 | 1 | undefined;
    alphaFiltering?: 0 | 2 | 1 | undefined;
    alphaQuality?: number | undefined;
    lossless?: 0 | 1 | undefined;
    emulateJpegSize?: 0 | 1 | undefined;
    threadLevel?: number | undefined;
    lowMemory?: 0 | 1 | undefined;
    nearLossless?: number | undefined;
    useDeltaPalette?: 0 | 1 | undefined;
    useSharpYuv?: 0 | 1 | undefined;
}>;
type WebpOptions = z.infer<typeof WebpOptionsSchema>;
export default function png(): {
    mime: "image/webp";
    hasAlpha: true;
    encode: (bitmap: import("@jimp/types").Bitmap, options?: Partial<WebpOptions>) => Promise<Buffer>;
    decode: (data: Buffer) => Promise<{
        data: Buffer;
        width: number;
        height: number;
    }>;
};
export {};
//# sourceMappingURL=index.d.ts.map