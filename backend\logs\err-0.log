You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
SyntaxError: Identifier 'inputStats' has already been declared
    at compileSourceTextModule (node:internal/modules/esm/utils:339:16)
    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:168:18)
    at callTranslator (node:internal/modules/esm/loader:428:14)
    at ModuleLoader.moduleProvider (node:internal/modules/esm/loader:434:30)
2025-07-13T15:42:07: === Global Error Handler ===
2025-07-13T15:42:07: Error: Only image files are allowed!
2025-07-13T15:42:07: Stack: Error: Only image files are allowed!
    at fileFilter (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:56:8)
    at wrappedFileFilter (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:519:28)
    at HeaderParser.cb (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:248:10)
    at SBMH.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:567:19)
2025-07-13T15:42:07: Request URL: /api/images/convert
2025-07-13T15:42:07: Request Method: POST
2025-07-13T15:42:07: Request Headers: {
  host: 'localhost:3001',
  'user-agent': 'curl/7.84.0',
  accept: '*/*',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.i9bHTc5BUAsrgREdGSDShq-V0nws_sOReI2y4JJw1v4',
  'content-length': '207179',
  'content-type': 'multipart/form-data; boundary=------------------------fb3ae89ec2062b17'
}
2025-07-13T15:42:51: === Global Error Handler ===
2025-07-13T15:42:51: Error: Only image files are allowed!
2025-07-13T15:42:51: Stack: Error: Only image files are allowed!
    at fileFilter (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:64:8)
    at wrappedFileFilter (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:519:28)
    at HeaderParser.cb (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:248:10)
    at SBMH.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:567:19)
2025-07-13T15:42:51: Request URL: /api/images/convert
2025-07-13T15:42:51: Request Method: POST
2025-07-13T15:42:51: Request Headers: {
  host: 'localhost:3001',
  'user-agent': 'curl/7.84.0',
  accept: '*/*',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.i9bHTc5BUAsrgREdGSDShq-V0nws_sOReI2y4JJw1v4',
  'content-length': '207179',
  'content-type': 'multipart/form-data; boundary=------------------------f029622ef5a5a56e'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:46:40: === UNHANDLED PROMISE REJECTION ===
2025-07-13T15:46:40: Reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:46:40: Promise: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
}
2025-07-13T15:46:40: Time: 2025-07-13T13:46:40.243Z
2025-07-13T15:46:40: Unhandled Rejection at: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
} reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:46:40: Stack: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:47:27: === UNHANDLED PROMISE REJECTION ===
2025-07-13T15:47:27: Reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:47:27: Promise: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
}
2025-07-13T15:47:27: Time: 2025-07-13T13:47:27.586Z
2025-07-13T15:47:27: Unhandled Rejection at: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
} reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:47:27: Stack: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:48:34: === Global Error Handler ===
2025-07-13T15:48:34: Error: Invalid refresh token.
2025-07-13T15:48:34: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:48:34: Request URL: /api/auth/refresh-token
2025-07-13T15:48:34: Request Method: POST
2025-07-13T15:48:34: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:48:34: === Global Error Handler ===
2025-07-13T15:48:34: Error: Invalid refresh token.
2025-07-13T15:48:34: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:48:34: Request URL: /api/auth/refresh-token
2025-07-13T15:48:34: Request Method: POST
2025-07-13T15:48:34: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:50:57: === Global Error Handler ===
2025-07-13T15:50:57: Error: Invalid refresh token.
2025-07-13T15:50:57: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:50:57: Request URL: /api/auth/refresh-token
2025-07-13T15:50:57: Request Method: POST
2025-07-13T15:50:57: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5174',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5174/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:50:57: === Global Error Handler ===
2025-07-13T15:50:57: Error: Invalid refresh token.
2025-07-13T15:50:57: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:50:57: Request URL: /api/auth/refresh-token
2025-07-13T15:50:57: Request Method: POST
2025-07-13T15:50:57: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5174',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5174/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:4
import Jimp from 'jimp';
       ^^^^
SyntaxError: The requested module 'jimp' does not provide an export named 'default'
    at ModuleJob._instantiate (node:internal/modules/esm/module_job:146:21)
    at async ModuleJob.run (node:internal/modules/esm/module_job:229:5)
    at async ModuleLoader.import (node:internal/modules/esm/loader:473:24)
