You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
SyntaxError: Identifier 'inputStats' has already been declared
    at compileSourceTextModule (node:internal/modules/esm/utils:339:16)
    at ModuleLoader.moduleStrategy (node:internal/modules/esm/translators:168:18)
    at callTranslator (node:internal/modules/esm/loader:428:14)
    at ModuleLoader.moduleProvider (node:internal/modules/esm/loader:434:30)
2025-07-13T15:42:07: === Global Error Handler ===
2025-07-13T15:42:07: Error: Only image files are allowed!
2025-07-13T15:42:07: Stack: Error: Only image files are allowed!
    at fileFilter (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:56:8)
    at wrappedFileFilter (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:519:28)
    at HeaderParser.cb (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:248:10)
    at SBMH.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:567:19)
2025-07-13T15:42:07: Request URL: /api/images/convert
2025-07-13T15:42:07: Request Method: POST
2025-07-13T15:42:07: Request Headers: {
  host: 'localhost:3001',
  'user-agent': 'curl/7.84.0',
  accept: '*/*',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.i9bHTc5BUAsrgREdGSDShq-V0nws_sOReI2y4JJw1v4',
  'content-length': '207179',
  'content-type': 'multipart/form-data; boundary=------------------------fb3ae89ec2062b17'
}
2025-07-13T15:42:51: === Global Error Handler ===
2025-07-13T15:42:51: Error: Only image files are allowed!
2025-07-13T15:42:51: Stack: Error: Only image files are allowed!
    at fileFilter (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:64:8)
    at wrappedFileFilter (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\index.js:44:7)
    at Multipart.<anonymous> (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\multer\lib\make-middleware.js:123:7)
    at Multipart.emit (node:events:519:28)
    at HeaderParser.cb (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:358:14)
    at HeaderParser.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:162:20)
    at SBMH.ssCb [as _cb] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:394:37)
    at feed (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:248:10)
    at SBMH.push (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\streamsearch\lib\sbmh.js:104:16)
    at Multipart._write (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\busboy\lib\types\multipart.js:567:19)
2025-07-13T15:42:51: Request URL: /api/images/convert
2025-07-13T15:42:51: Request Method: POST
2025-07-13T15:42:51: Request Headers: {
  host: 'localhost:3001',
  'user-agent': 'curl/7.84.0',
  accept: '*/*',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.i9bHTc5BUAsrgREdGSDShq-V0nws_sOReI2y4JJw1v4',
  'content-length': '207179',
  'content-type': 'multipart/form-data; boundary=------------------------f029622ef5a5a56e'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:46:40: === UNHANDLED PROMISE REJECTION ===
2025-07-13T15:46:40: Reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:46:40: Promise: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
}
2025-07-13T15:46:40: Time: 2025-07-13T13:46:40.243Z
2025-07-13T15:46:40: Unhandled Rejection at: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
} reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:46:40: Stack: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:47:27: === UNHANDLED PROMISE REJECTION ===
2025-07-13T15:47:27: Reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:47:27: Promise: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
}
2025-07-13T15:47:27: Time: 2025-07-13T13:47:27.586Z
2025-07-13T15:47:27: Unhandled Rejection at: Promise {
  <rejected> Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
      at async chmod (node:internal/fs/promises:1085:10)
      at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
    errno: -4058,
    code: 'ENOENT',
    syscall: 'chmod',
    path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
  }
} reason: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5) {
  errno: -4058,
  code: 'ENOENT',
  syscall: 'chmod',
  path: 'C:\\Users\\<USER>\\Documents\\GitHub\\SaaSSy\\backend\\node_modules\\node-webp\\webp\\windows-x64\\cwebp'
}
2025-07-13T15:47:27: Stack: Error: ENOENT: no such file or directory, chmod 'C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\webp\windows-x64\cwebp'
    at async chmod (node:internal/fs/promises:1085:10)
    at async grantPermissions (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\node-webp\dist\index.js:14:5)
2025-07-13T15:48:34: === Global Error Handler ===
2025-07-13T15:48:34: Error: Invalid refresh token.
2025-07-13T15:48:34: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:48:34: Request URL: /api/auth/refresh-token
2025-07-13T15:48:34: Request Method: POST
2025-07-13T15:48:34: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:48:34: === Global Error Handler ===
2025-07-13T15:48:34: Error: Invalid refresh token.
2025-07-13T15:48:34: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:48:34: Request URL: /api/auth/refresh-token
2025-07-13T15:48:34: Request Method: POST
2025-07-13T15:48:34: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:50:57: === Global Error Handler ===
2025-07-13T15:50:57: Error: Invalid refresh token.
2025-07-13T15:50:57: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:50:57: Request URL: /api/auth/refresh-token
2025-07-13T15:50:57: Request Method: POST
2025-07-13T15:50:57: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5174',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5174/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T15:50:57: === Global Error Handler ===
2025-07-13T15:50:57: Error: Invalid refresh token.
2025-07-13T15:50:57: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T15:50:57: Request URL: /api/auth/refresh-token
2025-07-13T15:50:57: Request Method: POST
2025-07-13T15:50:57: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5174',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5174/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
You have triggered an unhandledRejection, you may have forgotten to catch a Promise rejection:
file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:4
import Jimp from 'jimp';
       ^^^^
SyntaxError: The requested module 'jimp' does not provide an export named 'default'
    at ModuleJob._instantiate (node:internal/modules/esm/module_job:146:21)
    at async ModuleJob.run (node:internal/modules/esm/module_job:229:5)
    at async ModuleLoader.import (node:internal/modules/esm/loader:473:24)
2025-07-13T16:22:54: === Global Error Handler ===
2025-07-13T16:22:54: Error: Invalid refresh token.
2025-07-13T16:22:54: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T16:22:54: Request URL: /api/auth/refresh-token
2025-07-13T16:22:54: Request Method: POST
2025-07-13T16:22:54: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T16:22:54: === Global Error Handler ===
2025-07-13T16:22:54: Error: Invalid refresh token.
2025-07-13T16:22:54: Stack: UnauthorizedError: Invalid refresh token.
    at FakeAuthService.refreshAccessToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:190:15)
    at handleRefreshToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:83:43)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15
    at processParams (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:582:12)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:291:5)
2025-07-13T16:22:54: Request URL: /api/auth/refresh-token
2025-07-13T16:22:54: Request Method: POST
2025-07-13T16:22:54: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '194',
  'sec-ch-ua-platform': '"Windows"',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T16:39:54: JWT verification error: TokenExpiredError: jwt expired
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15 {
  expiredAt: 2025-07-13T14:38:00.000Z
}
2025-07-13T16:39:54: === Global Error Handler ===
2025-07-13T16:39:54: Error: Access token expired.
2025-07-13T16:39:54: Stack: UnauthorizedError: Access token expired.
    at file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:32:21
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:16
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
2025-07-13T16:39:54: Request URL: /api/purchases
2025-07-13T16:39:54: Request Method: GET
2025-07-13T16:39:54: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.7u2utkAxpLTIDeSCYuagFTG1UG5n79_ZjNAjM7B20G0',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6',
  'if-none-match': 'W/"e88-d/lQ/FN5ckyxqQCrScyReUoRsMk"'
}
2025-07-13T16:39:56: JWT verification error: TokenExpiredError: jwt expired
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15 {
  expiredAt: 2025-07-13T14:38:00.000Z
}
2025-07-13T16:39:56: === Global Error Handler ===
2025-07-13T16:39:56: Error: Access token expired.
2025-07-13T16:39:56: Stack: UnauthorizedError: Access token expired.
    at file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:32:21
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:16
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
2025-07-13T16:39:56: Request URL: /api/purchases
2025-07-13T16:39:56: Request Method: GET
2025-07-13T16:39:56: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.7u2utkAxpLTIDeSCYuagFTG1UG5n79_ZjNAjM7B20G0',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6',
  'if-none-match': 'W/"e88-d/lQ/FN5ckyxqQCrScyReUoRsMk"'
}
2025-07-13T16:39:57: JWT verification error: TokenExpiredError: jwt expired
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15 {
  expiredAt: 2025-07-13T14:38:00.000Z
}
2025-07-13T16:39:57: === Global Error Handler ===
2025-07-13T16:39:57: Error: Access token expired.
2025-07-13T16:39:57: Stack: UnauthorizedError: Access token expired.
    at file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:32:21
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:16
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
2025-07-13T16:39:57: Request URL: /api/purchases
2025-07-13T16:39:57: Request Method: GET
2025-07-13T16:39:57: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.7u2utkAxpLTIDeSCYuagFTG1UG5n79_ZjNAjM7B20G0',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6',
  'if-none-match': 'W/"e88-d/lQ/FN5ckyxqQCrScyReUoRsMk"'
}
2025-07-13T16:40:00: JWT verification error: TokenExpiredError: jwt expired
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:21
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:295:15 {
  expiredAt: 2025-07-13T14:38:00.000Z
}
2025-07-13T16:40:00: === Global Error Handler ===
2025-07-13T16:40:00: Error: Access token expired.
2025-07-13T16:40:00: Stack: UnauthorizedError: Access token expired.
    at file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:32:21
    at C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:190:16
    at getSecret (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:97:14)
    at module.exports [as verify] (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\jsonwebtoken\verify.js:101:10)
    at authenticateToken (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/middleware/authMiddleware.js:28:7)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
    at next (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\route.js:117:3)
    at handle (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\index.js:435:11)
    at Layer.handleRequest (C:\Users\<USER>\Documents\GitHub\SaaSSy\backend\node_modules\router\lib\layer.js:152:17)
2025-07-13T16:40:00: Request URL: /api/products/product-1/purchase
2025-07-13T16:40:00: Request Method: POST
2025-07-13T16:40:00: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '31',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.7u2utkAxpLTIDeSCYuagFTG1UG5n79_ZjNAjM7B20G0',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  accept: '*/*',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T17:33:20: === Global Error Handler ===
2025-07-13T17:33:20: Error: Invalid email or password.
2025-07-13T17:33:20: Stack: UnauthorizedError: Invalid email or password.
    at FakeAuthService.loginUser (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:62:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async login (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:35:20)
2025-07-13T17:33:20: Request URL: /api/auth/login
2025-07-13T17:33:20: Request Method: POST
2025-07-13T17:33:20: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '64',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.gEb1-1E7lo_HJdaH_kRBCbeSDZ0aWY8oOvNoAEcE_rE',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T17:33:20: === Global Error Handler ===
2025-07-13T17:33:20: Error: Invalid email or password.
2025-07-13T17:33:20: Stack: UnauthorizedError: Invalid email or password.
    at FakeAuthService.loginUser (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/fakeAuthService.js:62:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async login (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/authController.js:35:20)
2025-07-13T17:33:20: Request URL: /api/auth/login
2025-07-13T17:33:20: Request Method: POST
2025-07-13T17:33:20: Request Headers: {
  host: 'localhost:3001',
  connection: 'keep-alive',
  'content-length': '64',
  'sec-ch-ua-platform': '"Windows"',
  authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.43RRqRWW49kRV-HGFXIEvx4Eb6R_0wkUMX5UFnwtaJk',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  accept: 'application/json, text/plain, */*',
  'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
  'content-type': 'application/json',
  'sec-ch-ua-mobile': '?0',
  origin: 'http://localhost:5173',
  'sec-fetch-site': 'same-site',
  'sec-fetch-mode': 'cors',
  'sec-fetch-dest': 'empty',
  referer: 'http://localhost:5173/',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'de-DE,de;q=0.9,en-US;q=0.8,en;q=0.7,la;q=0.6'
}
2025-07-13T17:33:56: Error in addWatermark: TypeError: Jimp.loadFont is not a function
    at ImageProcessingService.addWatermark (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:122:31)
    at async ImageProcessingService.processImageWithTracking (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:265:18)
    at async file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:231:20
2025-07-13T18:10:38: Error in resizeImage: ZodError: [
  {
    "code": "invalid_union",
    "unionErrors": [
      {
        "issues": [
          {
            "code": "invalid_type",
            "expected": "object",
            "received": "number",
            "path": [],
            "message": "Expected object, received number"
          }
        ],
        "name": "ZodError"
      },
      {
        "issues": [
          {
            "code": "invalid_type",
            "expected": "object",
            "received": "number",
            "path": [],
            "message": "Expected object, received number"
          }
        ],
        "name": "ZodError"
      }
    ],
    "path": [],
    "message": "Invalid input"
  }
]
    at get error [as error] (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/node_modules/zod/v3/types.js:39:31)
    at ZodUnion.parse (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/node_modules/zod/v3/types.js:114:22)
    at Object.resize (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/node_modules/@jimp/plugin-resize/dist/esm/index.js:51:46)
    at Jimp.<computed> [as resize] (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/node_modules/@jimp/core/dist/esm/index.js:71:54)
    at ImageProcessingService.resizeImage (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:68:13)
    at async ImageProcessingService.processImageWithTracking (file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/services/imageProcessingService.js:262:18)
    at async file:///C:/Users/<USER>/Documents/GitHub/SaaSSy/backend/src/controllers/imageProcessingController.js:131:20 {
  issues: [
    {
      code: 'invalid_union',
      unionErrors: [Array],
      path: [],
      message: 'Invalid input'
    }
  ],
  addIssue: [Function (anonymous)],
  addIssues: [Function (anonymous)],
  errors: [
    {
      code: 'invalid_union',
      unionErrors: [Array],
      path: [],
      message: 'Invalid input'
    }
  ]
}
