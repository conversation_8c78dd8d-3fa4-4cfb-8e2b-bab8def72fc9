{"name": "@jsquash/webp", "version": "1.5.0", "main": "index.js", "description": "Wasm webp encoder and decoder supporting the browser. Repackaged from Squoosh App.", "repository": "jamsinclair/jSquash", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["image", "optimisation", "optimization", "squoosh", "wasm", "webassembly", "webp"], "license": "Apache-2.0", "scripts": {"clean": "rm -rf dist", "build:codec": "cd codec && npm run build", "build": "npm run clean && tsc && cp -r codec package.json README.md CHANGELOG.md *.d.ts .npmignore ../../LICENSE dist", "prepublishOnly": "[[ \"$PWD\" == *'/dist' ]] && exit 0 || (echo 'Please run npm publish from the dist directory' && exit 1)"}, "dependencies": {"wasm-feature-detect": "^1.2.11"}, "devDependencies": {"typescript": "^4.4.4"}, "type": "module", "sideEffects": false}