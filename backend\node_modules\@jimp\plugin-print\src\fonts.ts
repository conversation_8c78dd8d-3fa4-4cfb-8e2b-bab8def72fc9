import path from "path";
import { dirname } from "./dirname.js";

const dir = path.join(dirname, "../");

export const SANS_8_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt",
);

export const SANS_10_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt",
);

export const SANS_12_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt",
);

export const SANS_14_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt",
);

export const SANS_16_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt",
);

export const SANS_32_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt",
);

export const SANS_64_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt",
);

export const SANS_128_BLACK = path.join(
  dir,
  "fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt",
);

export const SANS_8_WHITE = path.join(
  dir,
  "fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt",
);

export const SANS_16_WHITE = path.join(
  dir,
  "fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt",
);

export const SANS_32_WHITE = path.join(
  dir,
  "fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt",
);

export const SANS_64_WHITE = path.join(
  dir,
  "fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt",
);

export const SANS_128_WHITE = path.join(
  dir,
  "fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt",
);
