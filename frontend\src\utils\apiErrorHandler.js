/**
 * Centralized API Error Handler
 * Provides consistent error handling across all API services
 */

export class ApiError extends Error {
  constructor(message, status, code, details = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

/**
 * Handle API errors consistently
 * @param {Error} error - The error object from axios or other sources
 * @returns {ApiError} - Standardized API error
 */
export function handleApiError(error) {
  console.error('=== API Error Handler ===');
  console.error('Original error:', error);

  // Network or connection errors
  if (!error.response) {
    if (error.request) {
      // Request was made but no response received
      console.error('Network error - no response received');
      return new ApiError(
        'Unable to connect to the server. Please check your internet connection.',
        0,
        'NETWORK_ERROR',
        { originalError: error.message }
      );
    } else {
      // Something else happened
      console.error('Request setup error:', error.message);
      return new ApiError(
        'An unexpected error occurred while making the request.',
        0,
        'REQUEST_ERROR',
        { originalError: error.message }
      );
    }
  }

  // HTTP response errors
  const { status, data } = error.response;
  console.error('HTTP error status:', status);
  console.error('HTTP error data:', data);

  // Extract error message and details from response
  let message = 'An unexpected error occurred';
  let code = 'UNKNOWN_ERROR';
  let details = null;

  if (data) {
    if (typeof data === 'string') {
      message = data;
    } else if (data.message) {
      message = data.message;
      code = data.code || code;
      details = data.details || data.error || null;
    } else if (data.error) {
      message = typeof data.error === 'string' ? data.error : data.error.message || message;
      code = data.error.code || code;
    }
  }

  // Handle specific HTTP status codes
  switch (status) {
    case 400:
      code = code === 'UNKNOWN_ERROR' ? 'BAD_REQUEST' : code;
      break;
    case 401:
      code = code === 'UNKNOWN_ERROR' ? 'UNAUTHORIZED' : code;
      message = message === 'An unexpected error occurred' ? 'Authentication required. Please log in.' : message;
      break;
    case 403:
      code = code === 'UNKNOWN_ERROR' ? 'FORBIDDEN' : code;
      message = message === 'An unexpected error occurred' ? 'You do not have permission to perform this action.' : message;
      break;
    case 404:
      code = code === 'UNKNOWN_ERROR' ? 'NOT_FOUND' : code;
      message = message === 'An unexpected error occurred' ? 'The requested resource was not found.' : message;
      break;
    case 422:
      code = code === 'UNKNOWN_ERROR' ? 'VALIDATION_ERROR' : code;
      break;
    case 429:
      code = code === 'UNKNOWN_ERROR' ? 'RATE_LIMIT' : code;
      message = message === 'An unexpected error occurred' ? 'Too many requests. Please try again later.' : message;
      break;
    case 500:
      code = code === 'UNKNOWN_ERROR' ? 'SERVER_ERROR' : code;
      message = message === 'An unexpected error occurred' ? 'Internal server error. Please try again later.' : message;
      break;
    case 502:
    case 503:
    case 504:
      code = code === 'UNKNOWN_ERROR' ? 'SERVICE_UNAVAILABLE' : code;
      message = message === 'An unexpected error occurred' ? 'Service temporarily unavailable. Please try again later.' : message;
      break;
  }

  return new ApiError(message, status, code, details);
}

/**
 * Show user-friendly error messages
 * @param {ApiError} error - The API error to display
 * @param {Object} options - Display options
 */
export function showErrorMessage(error, options = {}) {
  const {
    showToast = true,
    logToConsole = true,
    redirectOnAuth = true
  } = options;

  if (logToConsole) {
    console.error('Displaying error to user:', error);
  }

  // Handle authentication errors
  if (error.status === 401 && redirectOnAuth) {
    // Clear any stored auth data
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
    
    // Redirect to login page
    if (window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
    return;
  }

  // Show toast notification if available
  if (showToast && window.showToast) {
    window.showToast(error.message, 'error');
  }

  // Fallback to alert if no toast system
  if (showToast && !window.showToast) {
    alert(`Error: ${error.message}`);
  }
}

/**
 * Retry mechanism for failed requests
 * @param {Function} requestFn - The request function to retry
 * @param {Object} options - Retry options
 */
export async function retryRequest(requestFn, options = {}) {
  const {
    maxRetries = 3,
    delay = 1000,
    backoff = 2,
    retryCondition = (error) => error.status >= 500 || error.code === 'NETWORK_ERROR'
  } = options;

  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error instanceof ApiError ? error : handleApiError(error);
      
      if (attempt === maxRetries || !retryCondition(lastError)) {
        throw lastError;
      }
      
      const waitTime = delay * Math.pow(backoff, attempt - 1);
      console.log(`Request failed, retrying in ${waitTime}ms (attempt ${attempt}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
  
  throw lastError;
}
