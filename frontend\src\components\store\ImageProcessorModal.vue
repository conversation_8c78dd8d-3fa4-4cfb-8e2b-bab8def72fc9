<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div 
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        @click="$emit('close')"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
          <div>
            <h3 class="text-2xl font-bold text-gray-900">Image Processor</h3>
            <p class="text-gray-600 mt-1">
              {{ purchase?.product?.name }} - 
              {{ purchase?.usageCount || 0 }} / {{ purchase?.usageLimit === -1 ? 'Unlimited' : purchase?.usageLimit }} used
            </p>
          </div>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <!-- Tabs -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="-mb-px flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
              :class="activeTab === tab.id 
                ? 'border-blue-500 text-blue-600' 
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
            >
              {{ tab.name }}
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="min-h-96">
          <!-- Resize Tab -->
          <div v-if="activeTab === 'resize'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Upload Area -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Upload Image
                </label>
                <div
                  @drop="handleDrop"
                  @dragover.prevent
                  @dragenter.prevent
                  class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
                >
                  <input
                    ref="fileInput"
                    type="file"
                    accept="image/*"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                  <div v-if="!selectedFile">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">
                      <button @click="$refs.fileInput.click()" class="font-medium text-blue-600 hover:text-blue-500">
                        Click to upload
                      </button>
                      or drag and drop
                    </p>
                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                  </div>
                  <div v-else class="space-y-2">
                    <img :src="previewUrl" alt="Preview" class="mx-auto max-h-32 rounded" />
                    <p class="text-sm font-medium">{{ selectedFile.name }}</p>
                    <button @click="clearFile" class="text-red-600 hover:text-red-500 text-sm">
                      Remove
                    </button>
                  </div>
                </div>
              </div>

              <!-- Resize Options -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Resize Options
                </label>
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Width (px)</label>
                      <input
                        v-model.number="resizeOptions.width"
                        type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Auto"
                      />
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Height (px)</label>
                      <input
                        v-model.number="resizeOptions.height"
                        type="number"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Auto"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Fit Mode</label>
                    <select
                      v-model="resizeOptions.fit"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="cover">Cover (crop to fit)</option>
                      <option value="contain">Contain (fit within bounds)</option>
                      <option value="fill">Fill (stretch to fit)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Quality (1-100)</label>
                    <input
                      v-model.number="resizeOptions.quality"
                      type="range"
                      min="1"
                      max="100"
                      class="w-full"
                    />
                    <div class="text-center text-sm text-gray-600">{{ resizeOptions.quality }}%</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Button -->
            <div class="flex justify-end">
              <button
                @click="processResize"
                :disabled="!selectedFile || processing"
                class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-lg transition-colors"
              >
                {{ processing ? 'Processing...' : 'Resize Image' }}
              </button>
            </div>
          </div>

          <!-- Watermark Tab -->
          <div v-if="activeTab === 'watermark'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Upload Area (same as resize) -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Upload Image
                </label>
                <div
                  @drop="handleDrop"
                  @dragover.prevent
                  @dragenter.prevent
                  class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
                >
                  <input
                    ref="fileInput2"
                    type="file"
                    accept="image/*"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                  <div v-if="!selectedFile">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">
                      <button @click="$refs.fileInput2.click()" class="font-medium text-blue-600 hover:text-blue-500">
                        Click to upload
                      </button>
                      or drag and drop
                    </p>
                  </div>
                  <div v-else class="space-y-2">
                    <img :src="previewUrl" alt="Preview" class="mx-auto max-h-32 rounded" />
                    <p class="text-sm font-medium">{{ selectedFile.name }}</p>
                    <button @click="clearFile" class="text-red-600 hover:text-red-500 text-sm">
                      Remove
                    </button>
                  </div>
                </div>
              </div>

              <!-- Watermark Options -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Watermark Options
                </label>
                <div class="space-y-4">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Watermark Text</label>
                    <input
                      v-model="watermarkOptions.text"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="© Your Company"
                    />
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Position</label>
                    <select
                      v-model="watermarkOptions.position"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="bottom-right">Bottom Right</option>
                      <option value="bottom-left">Bottom Left</option>
                      <option value="top-right">Top Right</option>
                      <option value="top-left">Top Left</option>
                      <option value="center">Center</option>
                    </select>
                  </div>
                  
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Font Size</label>
                      <input
                        v-model.number="watermarkOptions.fontSize"
                        type="number"
                        min="8"
                        max="72"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label class="block text-xs font-medium text-gray-700 mb-1">Color</label>
                      <input
                        v-model="watermarkOptions.color"
                        type="color"
                        class="w-full h-10 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Opacity</label>
                    <input
                      v-model.number="watermarkOptions.opacity"
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.1"
                      class="w-full"
                    />
                    <div class="text-center text-sm text-gray-600">{{ Math.round(watermarkOptions.opacity * 100) }}%</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Button -->
            <div class="flex justify-end">
              <button
                @click="processWatermark"
                :disabled="!selectedFile || !watermarkOptions.text || processing"
                class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-lg transition-colors"
              >
                {{ processing ? 'Processing...' : 'Add Watermark' }}
              </button>
            </div>
          </div>

          <!-- Convert Tab -->
          <div v-if="activeTab === 'convert'" class="space-y-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Upload Area (same as others) -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Upload Image
                </label>
                <div
                  @drop="handleDrop"
                  @dragover.prevent
                  @dragenter.prevent
                  class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
                >
                  <input
                    ref="fileInput3"
                    type="file"
                    accept="image/*"
                    @change="handleFileSelect"
                    class="hidden"
                  />
                  <div v-if="!selectedFile">
                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                      <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="mt-2 text-sm text-gray-600">
                      <button @click="$refs.fileInput3.click()" class="font-medium text-blue-600 hover:text-blue-500">
                        Click to upload
                      </button>
                      or drag and drop
                    </p>
                  </div>
                  <div v-else class="space-y-2">
                    <img :src="previewUrl" alt="Preview" class="mx-auto max-h-32 rounded" />
                    <p class="text-sm font-medium">{{ selectedFile.name }}</p>
                    <button @click="clearFile" class="text-red-600 hover:text-red-500 text-sm">
                      Remove
                    </button>
                  </div>
                </div>
              </div>

              <!-- Convert Options -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  Convert Options
                </label>
                <div class="space-y-4">
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Target Format</label>
                    <select
                      v-model="convertOptions.format"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="jpg">JPG</option>
                      <option value="png">PNG</option>
                      <option value="webp">WebP</option>
                      <option value="gif">GIF</option>
                      <option value="tiff">TIFF</option>
                    </select>
                  </div>
                  
                  <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">Quality (1-100)</label>
                    <input
                      v-model.number="convertOptions.quality"
                      type="range"
                      min="1"
                      max="100"
                      class="w-full"
                    />
                    <div class="text-center text-sm text-gray-600">{{ convertOptions.quality }}%</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Process Button -->
            <div class="flex justify-end">
              <button
                @click="processConvert"
                :disabled="!selectedFile || processing"
                class="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-6 rounded-lg transition-colors"
              >
                {{ processing ? 'Processing...' : 'Convert Image' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Results -->
        <div v-if="result" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <div class="flex justify-between items-center">
            <div>
              <h4 class="font-medium text-green-800">Processing Complete!</h4>
              <p class="text-sm text-green-600 mt-1">
                Processing time: {{ result.processingTime }}ms | 
                Size: {{ formatFileSize(result.originalSize) }} → {{ formatFileSize(result.processedSize) }}
              </p>
            </div>
            <a
              :href="result.downloadUrl"
              download
              class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Download
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useAuthStore } from '@/store/authStore'


export default {
  name: 'ImageProcessorModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    purchase: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'usage-updated'],
  data() {
    return {
      activeTab: 'resize',
      selectedFile: null,
      previewUrl: null,
      processing: false,
      result: null,
      tabs: [
        { id: 'resize', name: 'Resize' },
        { id: 'watermark', name: 'Watermark' },
        { id: 'convert', name: 'Convert' }
      ],
      resizeOptions: {
        width: null,
        height: null,
        fit: 'cover',
        quality: 80
      },
      watermarkOptions: {
        text: '',
        position: 'bottom-right',
        opacity: 0.7,
        fontSize: 24,
        color: '#ffffff'
      },
      convertOptions: {
        format: 'jpg',
        quality: 80
      }
    }
  },
  setup() {
    const authStore = useAuthStore()
    return {
      authStore
    }
  },
  computed: {
    token() {
      return this.authStore.getAccessToken
    }
  },
  methods: {
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.selectedFile = file;
        this.previewUrl = URL.createObjectURL(file);
        this.result = null;
      }
    },
    
    handleDrop(event) {
      event.preventDefault();
      const file = event.dataTransfer.files[0];
      if (file && file.type.startsWith('image/')) {
        this.selectedFile = file;
        this.previewUrl = URL.createObjectURL(file);
        this.result = null;
      }
    },
    
    clearFile() {
      this.selectedFile = null;
      if (this.previewUrl) {
        URL.revokeObjectURL(this.previewUrl);
        this.previewUrl = null;
      }
      this.result = null;
    },
    
    async processResize() {
      await this.processImage('resize', this.resizeOptions);
    },
    
    async processWatermark() {
      await this.processImage('watermark', this.watermarkOptions);
    },
    
    async processConvert() {
      await this.processImage('convert', this.convertOptions);
    },
    
    async processImage(operation, options) {
      if (!this.selectedFile) return;

      this.processing = true;
      this.result = null;

      try {
        // Ensure we have a valid token before making the request
        const isValid = await this.authStore.ensureValidToken();
        if (!isValid) {
          throw new Error('Authentication required. Please log in again.');
        }

        // Get the current token (might be refreshed)
        const currentToken = this.authStore.getAccessToken;
        if (!currentToken) {
          throw new Error('No authentication token available');
        }

        const formData = new FormData();
        formData.append('image', this.selectedFile);

        // Add operation-specific options
        Object.keys(options).forEach(key => {
          if (options[key] !== null && options[key] !== '') {
            formData.append(key, options[key]);
          }
        });

        const response = await fetch(`http://localhost:3001/api/images/${operation}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${currentToken}`
          },
          body: formData
        });

        const data = await response.json();

        if (response.ok) {
          this.result = data.data;
          this.$emit('usage-updated');
        } else {
          if (response.status === 401) {
            // Token might have expired between validation and request
            this.authStore.logoutAction();
            throw new Error('Session expired. Please log in again.');
          }
          throw new Error(data.message || data.error || 'Processing failed');
        }
      } catch (error) {
        console.error('Error processing image:', error);

        // Show user-friendly error messages
        let errorMessage = error.message;
        if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
          errorMessage = 'Session expired. Please log in again.';
          this.$router.push('/login');
        } else if (errorMessage.includes('403') || errorMessage.includes('Forbidden')) {
          errorMessage = 'You do not have permission to use this feature.';
        } else if (errorMessage.includes('500') || errorMessage.includes('Internal Server Error')) {
          errorMessage = 'Server error. Please try again later.';
        }

        alert('Processing failed: ' + errorMessage);
      } finally {
        this.processing = false;
      }
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  },
  
  beforeUnmount() {
    if (this.previewUrl) {
      URL.revokeObjectURL(this.previewUrl);
    }
  }
}
</script>
