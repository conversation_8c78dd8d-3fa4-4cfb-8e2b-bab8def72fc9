//const jwt = require('jsonwebtoken');
import jwt from 'jsonwebtoken';
//const { JWT_SECRET } = require('../config/jwtConfig');
import { JWT_SECRET } from '../config/jwtConfig.js';
//const { UnauthorizedError, BadRequestError } = require('../utils/errors');
import { UnauthorizedError, BadRequestError } from '../utils/errors.js';

export const authenticateToken = (req, res, next) => {
  console.log('Auth middleware called for:', req.method, req.url);

  // Check if fake auth mode is enabled
  if (process.env.FAKE_AUTH_MODE === 'true') {
    console.log('Fake auth mode enabled - bypassing JWT verification');
    // In fake auth mode, create a fake user for testing
    req.user = {
      userId: 'user-1', // Default test user
      email: '<EMAIL>',
      role: 'CUSTOMER',
      isActive: true
    };
    return next();
  }

  const authHeader = req.headers['authorization'];

  // Check if the Authorization header is present and starts with 'Bearer '
  // If not, return an UnauthorizedError with a specific message

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    console.log('ERROR: Malformed or missing auth header');
    return next(new UnauthorizedError('Malformed token. Expected Bearer token.'));
  }

  const token = authHeader.split(' ')[1];

  if (!token) {
    // This case handles 'Bearer ' (with a space but no token)
    return next(new UnauthorizedError('Access token is required.'));
  }

  jwt.verify(token, JWT_SECRET, (err, decodedPayload) => {
    if (err) {
      console.error('JWT verification error:', err);
      if (err.name === 'TokenExpiredError') {
        return next(new UnauthorizedError('Access token expired.'));
      }
      // For other errors like JsonWebTokenError (malformed token, invalid signature)
      return next(new UnauthorizedError('Invalid access token.'));
    }

    // Token is valid, attach payload to request object
    // Payload should contain userId, role, entitlements as per project doc
    console.log('Decoded JWT payload:', decodedPayload);
    req.user = decodedPayload;

    next();
  });
};

