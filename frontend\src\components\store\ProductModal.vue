<template>
  <div v-if="isOpen && product" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div 
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
        @click="$emit('close')"
      ></div>

      <!-- Modal panel -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
          <div>
            <h3 class="text-2xl font-bold text-gray-900">{{ product.name }}</h3>
            <p class="text-gray-600 mt-1">{{ product.shortDescription }}</p>
          </div>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- Left Column - Product Info -->
          <div>
            <!-- Product Image -->
            <img 
              :src="product.imageUrl || '/images/placeholder-product.jpg'" 
              :alt="product.name"
              class="w-full h-64 object-cover rounded-lg mb-6"
            />

            <!-- Description -->
            <div class="mb-6">
              <h4 class="text-lg font-semibold mb-3">Description</h4>
              <p class="text-gray-700 leading-relaxed">{{ product.description }}</p>
            </div>

            <!-- Features -->
            <div v-if="features.length" class="mb-6">
              <h4 class="text-lg font-semibold mb-3">Features</h4>
              <ul class="space-y-2">
                <li 
                  v-for="feature in features" 
                  :key="feature"
                  class="flex items-center text-gray-700"
                >
                  <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ feature }}
                </li>
              </ul>
            </div>

            <!-- Usage Limits -->
            <div v-if="limits" class="mb-6">
              <h4 class="text-lg font-semibold mb-3">Usage Limits</h4>
              <div class="bg-gray-50 p-4 rounded-lg">
                <div v-if="limits.monthlyProcessing" class="mb-2">
                  <span class="font-medium">Monthly Processing:</span>
                  <span class="ml-2">{{ limits.monthlyProcessing === -1 ? 'Unlimited' : limits.monthlyProcessing }}</span>
                </div>
                <div v-if="limits.maxFileSize" class="mb-2">
                  <span class="font-medium">Max File Size:</span>
                  <span class="ml-2">{{ limits.maxFileSize }}</span>
                </div>
                <div v-if="limits.supportedFormats">
                  <span class="font-medium">Supported Formats:</span>
                  <div class="mt-1 flex flex-wrap gap-1">
                    <span 
                      v-for="format in limits.supportedFormats" 
                      :key="format"
                      class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs uppercase"
                    >
                      {{ format }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Purchase Info -->
          <div>
            <!-- Pricing -->
            <div class="bg-gray-50 p-6 rounded-lg mb-6">
              <div class="text-center mb-4">
                <div class="text-4xl font-bold text-blue-600 mb-2">
                  ${{ formatPrice(product.basePrice) }}
                </div>
                <div v-if="product.billingType === 'MONTHLY'" class="text-gray-600">
                  per month
                </div>
                <div v-else-if="product.billingType === 'YEARLY'" class="text-gray-600">
                  per year
                </div>
                <div v-else class="text-gray-600">
                  one-time payment
                </div>
              </div>

              <!-- Purchase Button -->
              <button
                @click="handlePurchase"
                :disabled="isOwned || isLoading"
                class="w-full font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                :class="isOwned 
                  ? 'bg-green-100 text-green-800 cursor-not-allowed' 
                  : isLoading
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'"
              >
                <span v-if="isLoading">Processing...</span>
                <span v-else-if="isOwned">Already Owned</span>
                <span v-else>Purchase Now</span>
              </button>

              <p class="text-xs text-gray-500 text-center mt-3">
                Secure payment processing
              </p>
            </div>

            <!-- Reviews Summary -->
            <div v-if="product.reviewStats && product.reviewStats.totalReviews > 0" class="mb-6">
              <h4 class="text-lg font-semibold mb-3">Customer Reviews</h4>
              <div class="flex items-center mb-3">
                <div class="flex">
                  <svg 
                    v-for="star in 5" 
                    :key="star"
                    class="w-5 h-5"
                    :class="star <= product.reviewStats.averageRating ? 'text-yellow-400' : 'text-gray-300'"
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                </div>
                <span class="ml-3 text-lg font-medium">
                  {{ product.reviewStats.averageRating.toFixed(1) }}
                </span>
                <span class="ml-2 text-gray-600">
                  ({{ product.reviewStats.totalReviews }} reviews)
                </span>
              </div>

              <!-- Rating Distribution -->
              <div class="space-y-1">
                <div 
                  v-for="rating in [5, 4, 3, 2, 1]" 
                  :key="rating"
                  class="flex items-center text-sm"
                >
                  <span class="w-3">{{ rating }}</span>
                  <svg class="w-4 h-4 text-yellow-400 mx-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  <div class="flex-1 mx-2 bg-gray-200 rounded-full h-2">
                    <div 
                      class="bg-yellow-400 h-2 rounded-full"
                      :style="{ width: `${(product.reviewStats.ratingDistribution[rating] / product.reviewStats.totalReviews) * 100}%` }"
                    ></div>
                  </div>
                  <span class="text-gray-600 text-xs">
                    {{ product.reviewStats.ratingDistribution[rating] }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Recent Reviews -->
            <div v-if="product.reviews && product.reviews.length" class="mb-6">
              <h4 class="text-lg font-semibold mb-3">Recent Reviews</h4>
              <div class="space-y-4">
                <div 
                  v-for="review in product.reviews.slice(0, 2)" 
                  :key="review.id"
                  class="border-l-4 border-blue-200 pl-4"
                >
                  <div class="flex items-center mb-1">
                    <div class="flex">
                      <svg 
                        v-for="star in review.rating" 
                        :key="star"
                        class="w-4 h-4 text-yellow-400"
                        fill="currentColor" 
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                      </svg>
                    </div>
                    <span class="ml-2 font-medium text-sm">{{ review.user?.name || 'Anonymous' }}</span>
                    <span v-if="review.isVerified" class="ml-2 text-green-600 text-xs">✓ Verified</span>
                  </div>
                  <h5 v-if="review.title" class="font-medium text-sm mb-1">{{ review.title }}</h5>
                  <p class="text-gray-700 text-sm">{{ review.content }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductModal',
  props: {
    isOpen: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      required: false,
      default: () => ({
        name: '',
        shortDescription: '',
        description: '',
        imageUrl: '',
        features: '[]',
        priceCents: 0,
        category: ''
      })
    },
    userPurchases: {
      type: Array,
      default: () => []
    }
  },
  emits: ['close', 'purchase'],
  data() {
    return {
      isLoading: false
    }
  },
  computed: {
    features() {
      try {
        return JSON.parse(this.product.features || '[]');
      } catch {
        return [];
      }
    },
    limits() {
      try {
        return JSON.parse(this.product.limits || '{}');
      } catch {
        return {};
      }
    },
    isOwned() {
      return this.userPurchases.some(purchase => 
        purchase.productId === this.product.id && 
        purchase.status === 'ACTIVE'
      );
    }
  },
  methods: {
    formatPrice(priceInCents) {
      return (priceInCents / 100).toFixed(2);
    },
    async handlePurchase() {
      if (this.isOwned || this.isLoading) return;
      
      this.isLoading = true;
      try {
        await this.$emit('purchase', this.product);
      } finally {
        this.isLoading = false;
      }
    }
  }
}
</script>
